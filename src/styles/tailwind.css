@import 'tailwindcss';

@theme {
  /* Screens */
  --breakpoint-xs: 390px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;

  /* Font weights */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font families */
  --font-family-inter: Inter, sans-serif;

  /* Border radius */
  --radius-xxl: 1.5rem;
  --radius-1xl: 0.875rem;

  /* Box shadows */
  --shadow-container: 0px 5px 20px 0px rgba(42, 40, 135, 0.05);
  --shadow-hover: 0px 6px 15px 0px rgba(42, 40, 135, 0.07);
  --shadow-inset-dialog-gray: 0px 0px 0px 1px var(--color-neutral-200) inset;

  /* Container */
  --container-center: true;
  --container-padding: 2rem;

  /* Transition delays */
  --transition-delay-3000: 3000ms;

  /* Animations */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-loader: loader 0.6s infinite alternate;
  --animate-fade-out: fade-out 2s ease 0s 1 normal forwards;
  --animate-ride-in-right: ride-in-right 2s ease 0s 1 normal forwards;
  --animate-ride-in-left: ride-in-left 2s ease 0s 1 normal forwards;
  --animate-fade: fade 1.25s ease-out forwards;
  --animate-indicator: indicator 2s ease-in-out infinite;
  --animate-pulse-and-pump: pulse-and-pump 0.5s
    cubic-bezier(0.21, 0.35, 0.44, 0.99) infinite;
  --animate-pulse: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.4s infinite;
  --animate-pulse-2: pulse 0.3s cubic-bezier(0.21, 0.35, 0.44, 0.99) 0.7s
    infinite;
  --animate-pulse-3: slow-fade-pulse 2s cubic-bezier(0, 0.85, 0, 0.75) 0.9s
    infinite;
  --animate-rotate-user: rotate-user 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-user: rotate-reverse-user 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-euro: rotate-euro 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-euro: rotate-reverse-euro 23.4s ease-in-out 3.9s
    infinite;
  --animate-rotate-bank: rotate-bank 23.4s ease-in-out 3.9s infinite;
  --animate-rotate-reverse-bank: rotate-reverse-bank 23.4s ease-in-out 3.9s
    infinite;
  --animate-pulse-opacity: pulse-opacity 2s cubic-bezier(0.4, 0, 0.6, 1)
    infinite;
  --animate-lds-spinner: lds-spinner 1.2s linear infinite;

  /* Radix UI Animations */
  --animate-in: animate-in 0.6s ease-out;
  --animate-out: animate-out 0.2s ease-in;
  --animate-fade-in: fade-in 0.2s ease-out;
  --animate-fade-out: fade-out 0.2s ease-in;
  --animate-zoom-in: zoom-in 0.2s ease-out;
  --animate-zoom-out: zoom-out 0.2s ease-in;
  --animate-slide-in-from-top-2: slide-in-from-top-2 0.2s ease-out;
  --animate-slide-in-from-bottom-2: slide-in-from-bottom-2 0.2s ease-out;
  --animate-slide-in-from-left-2: slide-in-from-left-2 0.2s ease-out;
  --animate-slide-in-from-right-2: slide-in-from-right-2 0.2s ease-out;

  /* Primary */
  --color-primary-brand-02: #3300ff;

  --color-primary-black: #000000;
  --color-primary-white: #ffffff;
  /* System */
  --color-system-green: #38cf86;
  --color-system-green-800: #085d3b;
  --color-system-yellow: #ead40d;
  --color-system-yellow-400: #fbbf24;
  --color-system-orange: #ea770d;
  --color-system-red: #ea350d;
  /* Neutral */
  --color-neutral-900: #111827;
  --color-neutral-800: #1f2937;
  --color-neutral-700: #374151;
  --color-neutral-600: #4b5563;
  --color-neutral-500: #6b7280;
  --color-neutral-400: #9ca3af;
  --color-neutral-300: #d1d5db;
  --color-neutral-200: #e5e7eb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-50: #f9fafb;

  /* Typography Colors (using color-mix for alpha support) */
  --color-typography-black: color-mix(
    in srgb,
    var(--color-primary-black),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-neutral400: color-mix(
    in srgb,
    var(--color-neutral-400),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-neutral300: color-mix(
    in srgb,
    var(--color-neutral-300),
    transparent calc(100% - 100% * <alpha-value>)
  );
  --color-typography-white: color-mix(
    in srgb,
    var(--color-primary-white),
    transparent calc(100% - 100% * <alpha-value>)
  );

  /* Border Colors (using color-mix for alpha support) */
  --color-borders-neutral200: color-mix(
    in srgb,
    var(--color-neutral-200),
    transparent calc(100% - 100% * <alpha-value>)
  );

  /* UI Component Colors (HSL-based, referencing :root variables) */
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));
  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));
  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));
  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));
  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));
  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
}

/* Keyframes for animations */
@keyframes loader {
  to {
    opacity: 0.1;
    transform: translate3d(0, -0.25rem, 0);
  }
}

@keyframes lds-spinner {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes ride-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes ride-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fade {
  0% {
    opacity: 1;
    height: 100%;
  }
  50% {
    opacity: 1;
    height: 100%;
  }
  100% {
    opacity: 0;
    height: 0;
  }
}

@keyframes indicator {
  0% {
    width: 0%;
    height: 0%;
    opacity: 0;
  }
  15% {
    opacity: 1;
  }
  100% {
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes pulse-and-pump {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: scale(1);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: scale(0.95);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: scale(0.9);
  }
  18.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: scale(0.85);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: scale(0.8);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 0%);
    transform: scale(0.75);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 13.2%);
    transform: scale(0.8);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 26.4%);
    transform: scale(0.85);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 39.6%);
    transform: scale(0.9);
  }
  56.25% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 52.8%);
    transform: scale(0.95);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
    transform: scale(1);
  }
  68.75%,
  75%,
  81.25%,
  87.5%,
  93.75%,
  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulse {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }
  18.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 45%, #fcfcfc 66%);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #fcfcfc 66%);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 27%, #fcfcfc 66%);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #fcfcfc 66%);
  }
  56.25% {
    background: radial-gradient(circle at center, #e2e2e2 9%, #fcfcfc 66%);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 #fcfcfc 66%);
  }
  68.75%,
  75%,
  81.25%,
  87.5%,
  93.75%,
  100% {
    background: radial-gradient(circle at center, #e2e2e2, #fcfcfc 66%);
  }
}

@keyframes slow-fade-pulse {
  0% {
    background: radial-gradient(circle at center, #e2e2e2, #ffffff 70%);
  }
  3.125% {
    background: radial-gradient(circle at center, #e2e2e2 10%, #ffffff 70%);
  }
  6.25% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }
  9.375% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }
  12.5% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }
  15.625%,
  21.875% {
    background: radial-gradient(circle at center, #e2e2e2 40%, #ffffff 70%);
  }
  25% {
    background: radial-gradient(circle at center, #e2e2e2 38%, #ffffff 70%);
  }
  31.25% {
    background: radial-gradient(circle at center, #e2e2e2 36%, #ffffff 70%);
  }
  37.5% {
    background: radial-gradient(circle at center, #e2e2e2 34%, #ffffff 70%);
  }
  43.75% {
    background: radial-gradient(circle at center, #e2e2e2 32%, #ffffff 70%);
  }
  46.875% {
    background: radial-gradient(circle at center, #e2e2e2 30%, #ffffff 70%);
  }
  50% {
    background: radial-gradient(circle at center, #e2e2e2 28%, #ffffff 70%);
  }
  53.125% {
    background: radial-gradient(circle at center, #e2e2e2 26%, #ffffff 70%);
  }
  62.5% {
    background: radial-gradient(circle at center, #e2e2e2 24%, #ffffff 70%);
  }
  65.625% {
    background: radial-gradient(circle at center, #e2e2e2 22%, #ffffff 70%);
  }
  68.75% {
    background: radial-gradient(circle at center, #e2e2e2 20%, #ffffff 70%);
  }
  71.875% {
    background: radial-gradient(circle at center, #e2e2e2 18%, #ffffff 70%);
  }
  75% {
    background: radial-gradient(circle at center, #e2e2e2 16%, #ffffff 70%);
  }
  100% {
    background: radial-gradient(circle at center, #e2e2e2 0%, #ffffff 70%);
  }
}

@keyframes rotate-user {
  0% {
    transform: rotate(-90deg);
  }
  12.39% {
    transform: rotate(-90deg);
  }
  16.66% {
    transform: rotate(30deg);
  }
  45.71% {
    transform: rotate(30deg);
  }
  49.98% {
    transform: rotate(180deg);
  }
  79.48% {
    transform: rotate(180deg);
  }
  83.75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(270deg);
  }
}

@keyframes rotate-reverse-user {
  0% {
    transform: rotate(90deg);
  }
  12.39% {
    transform: rotate(90deg);
  }
  16.66% {
    transform: rotate(-30deg);
  }
  45.71% {
    transform: rotate(-30deg);
  }
  49.98% {
    transform: rotate(-180deg);
  }
  79.48% {
    transform: rotate(-180deg);
  }
  83.75% {
    transform: rotate(-270deg);
  }
  100% {
    transform: rotate(-270deg);
  }
}

@keyframes rotate-euro {
  0% {
    transform: rotate(90deg);
  }
  29.05% {
    transform: rotate(90deg);
  }
  33.32% {
    transform: rotate(180deg);
  }
  45.57% {
    transform: rotate(180deg);
  }
  49.98% {
    transform: rotate(245deg);
  }
  62.37% {
    transform: rotate(245deg);
  }
  79.03% {
    transform: rotate(245deg);
  }
  83.3% {
    transform: rotate(300deg);
  }
  95.69% {
    transform: rotate(300deg);
  }
  100% {
    transform: rotate(450deg);
  }
}

@keyframes rotate-reverse-euro {
  0% {
    transform: rotate(-90deg);
  }
  29.05% {
    transform: rotate(-90deg);
  }
  33.32% {
    transform: rotate(-180deg);
  }
  45.57% {
    transform: rotate(-180deg);
  }
  49.98% {
    transform: rotate(-245deg);
  }
  62.37% {
    transform: rotate(-245deg);
  }
  79.03% {
    transform: rotate(-245deg);
  }
  83.3% {
    transform: rotate(-300deg);
  }
  95.69% {
    transform: rotate(-300deg);
  }
  100% {
    transform: rotate(-450deg);
  }
}

@keyframes rotate-bank {
  0% {
    transform: rotate(0deg);
  }
  45.71% {
    transform: rotate(0deg);
  }
  49.98% {
    transform: rotate(45deg);
  }
  62.37% {
    transform: rotate(45deg);
  }
  66.64% {
    transform: rotate(170deg);
  }
  79.03% {
    transform: rotate(170deg);
  }
  83.3% {
    transform: rotate(245deg);
  }
  95.69% {
    transform: rotate(245deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate-reverse-bank {
  0% {
    transform: rotate(0deg);
  }
  45.71% {
    transform: rotate(0deg);
  }
  49.98% {
    transform: rotate(-45deg);
  }
  62.37% {
    transform: rotate(-45deg);
  }
  66.64% {
    transform: rotate(-170deg);
  }
  79.03% {
    transform: rotate(-170deg);
  }
  83.3% {
    transform: rotate(-245deg);
  }
  95.69% {
    transform: rotate(-245deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* Radix UI Animation Keyframes */
@keyframes animate-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes animate-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoom-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoom-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes slide-in-from-top-2 {
  from {
    transform: translateY(-0.5rem);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom-2 {
  from {
    transform: translateY(0.5rem);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-top {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-left-2 {
  from {
    transform: translateX(-0.5rem);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-from-right-2 {
  from {
    transform: translateX(0.5rem);
  }
  to {
    transform: translateX(0);
  }
}

@layer base {
  /* CSS Reset - moved here to ensure proper cascade order with Tailwind utilities */
  html,
  body,
  div,
  span,
  applet,
  object,
  iframe,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  blockquote,
  pre,
  a,
  abbr,
  acronym,
  address,
  big,
  cite,
  code,
  del,
  dfn,
  em,
  img,
  ins,
  kbd,
  q,
  s,
  samp,
  small,
  strike,
  strong,
  sub,
  sup,
  tt,
  var,
  b,
  u,
  i,
  center,
  dl,
  dt,
  dd,
  ol,
  ul,
  li,
  fieldset,
  option,
  form,
  label,
  legend,
  table,
  caption,
  tbody,
  tfoot,
  thead,
  tr,
  th,
  td,
  article,
  aside,
  canvas,
  details,
  embed,
  figure,
  figcaption,
  footer,
  header,
  hgroup,
  menu,
  nav,
  output,
  ruby,
  section,
  summary,
  time,
  mark,
  audio,
  input,
  video {
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    /* stylelint-disable-next-line declaration-block-no-shorthand-property-overrides */
    font: inherit;
    vertical-align: baseline;
  }

  /* HTML5 display-role reset for older browsers */
  article,
  aside,
  details,
  figcaption,
  figure,
  footer,
  header,
  hgroup,
  menu,
  nav,
  section {
    display: block;
  }

  body {
    line-height: 1;
  }

  ol,
  ul {
    list-style: none;
  }

  blockquote,
  q {
    quotes: none;
  }

  blockquote::before,
  blockquote::after,
  q::before,
  q::after {
    content: '';
    content: none;
  }

  table {
    border-collapse: collapse;
    border-spacing: 0;
  }

  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }

  /* This is needed to fix the z-index of the popover */
  [data-radix-popper-content-wrapper] {
    z-index: 300 !important;
  }
}

@layer base {
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  body:has(.app-modal) {
    overflow: hidden;
  }

  /* Restore semantic HTML styling that was reset */
  strong,
  b {
    font-weight: var(--font-weight-bold);
  }
}

/* Our configs */
/* Color design system */
/* Link => https://www.figma.com/file/qRNfEkalc67TUhyFm6AjFf/ESTO-Design-System?type=design&node-id=1-6481&mode=design&t=cv07M8IvBhRx2JAL-0 */

@layer base {
  html,
  body,
  #root {
    /* font-family-inter */
    height: 100%;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .underline-from-font {
    text-underline-position: from-font;
  }
}
