import { PageAttributeNames } from 'app-constants';
import { AppLoader } from 'components';
import {
  AddCompanyDetailsField,
  ContactExtraForm,
  SpouseSendInstructions,
} from 'components/contact-extra-form';
import {
  type SmallLoanContactExtraFormType,
  useContactExtraPageLogicModern,
} from 'hooks/page-logic/small-loan/use-contact-extra-page-logic-modern';

const ContactExtraPage = () => {
  const {
    form,
    onContactExtraFormSubmit,
    contactExtraPageLoaded,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    sendingConsentLinkValidationErrors,
    instructionsSent,
    setInstructionsSent,
    sendingConsentLink,
    occupationCategoryOptions,
    legalPeopleOptions,
    employmentDateOptions,
    addLegalPersonToInvoiceDisabled,
    legalPeopleLoading,
    handleSendConsentLink,
    onAddLegalPersonToInvoiceChange,
  } = useContactExtraPageLogicModern();

  if (!contactExtraPageLoaded) {
    return <AppLoader isRelative />;
  }

  return (
    <ContactExtraForm<SmallLoanContactExtraFormType>
      form={form}
      onSubmit={onContactExtraFormSubmit}
      visiblePageAttributes={visiblePageAttributes}
      validationErrors={userInfoExtraValidationErrors}
      occupationCategoryOptions={occupationCategoryOptions}
      employmentDateOptions={employmentDateOptions}
      isSubmitting={form.formState.isSubmitting}
    >
      <AddCompanyDetailsField
        form={form}
        isDisabled={addLegalPersonToInvoiceDisabled}
        legalPeopleLoading={legalPeopleLoading}
        onChange={onAddLegalPersonToInvoiceChange}
        selectOptions={legalPeopleOptions}
        visible={
          visiblePageAttributes[PageAttributeNames.addLegalPersonToInvoice]
        }
      />

      <SpouseSendInstructions
        form={form}
        onSendInstructions={handleSendConsentLink}
        sendingConsentLink={sendingConsentLink}
        validationErrors={sendingConsentLinkValidationErrors}
        instructionsSent={instructionsSent}
        onInstructionsSentChange={setInstructionsSent}
        visible={
          visiblePageAttributes[PageAttributeNames.spouseInstructionsSection]
        }
      />
    </ContactExtraForm>
  );
};

export default ContactExtraPage;
