import { CreditLineRoutePaths } from 'app-constants';
import { DefaultLoader } from 'components/default-loader/DefaultLoader';
import { ScrollIntoView } from 'components/scroll-into-view';
import { useRootContext } from 'context/root';
import { useCheckIsEqualCurrentPageUrl, usePageUrlSuspenseState } from 'hooks';
import { useIsMobileView } from 'hooks/system';
import { Suspense } from 'react';
import Skeleton from 'react-loading-skeleton';
import { Outlet, useLocation } from 'react-router-dom';
import { cn } from 'utils/tailwind';
import { MainFooter } from 'widgets/footers/main-footer';
import { MainHeader } from 'widgets/headers/main-header';
import { ContainerLayout } from 'widgets/layouts/container-layout';
import { PageLayout } from 'widgets/layouts/page-layout/PageLayout';

import { SmallLoanInfoPage } from './small-loan-info-page/ui/SmallLoanInfoPage';

export const Root = () => {
  const { pathname } = useLocation();
  const isMobileView = useIsMobileView();
  const { user } = useRootContext();
  const isLoginPath = pathname.includes(CreditLineRoutePaths.LOGIN);
  const isCheckoutPath = pathname.includes(CreditLineRoutePaths.CHECKOUT);
  const isSigningUpPath = pathname.includes(CreditLineRoutePaths.SIGNING);
  const isSpouseConsentPath = pathname.includes(CreditLineRoutePaths.SPOUSE);
  const isStatusPath =
    pathname.includes(CreditLineRoutePaths.REJECT) ||
    pathname.includes(CreditLineRoutePaths.PENDING) ||
    pathname.includes(CreditLineRoutePaths.SUCCESS);

  const { loading, data } = usePageUrlSuspenseState();

  const isEqualCurrentPageUrl = useCheckIsEqualCurrentPageUrl();

  const renderLeftBlock = () => {
    const isContentReady =
      isSpouseConsentPath ||
      (data?.page_url && !loading) ||
      isEqualCurrentPageUrl;

    if (!isContentReady) {
      return <LeftBlockSkeleton />;
    }

    if (user?.id && isSigningUpPath) {
      return 'APPROVED LOAN AMOUNT BLOCK'; //TODO here should be approved loan amount block;
    }

    return <SmallLoanInfoPage />;
  };

  return (
    <>
      <PageLayout
        withLeftContainer={false}
        className={cn(
          'font-family-inter',
          isMobileView &&
            (isCheckoutPath || isLoginPath || isSigningUpPath) &&
            '[&_#rightBody]:border-t [&_#rightBody]:border-solid [&_#rightBody]:border-neutral-200 [&_#rightBody]:rounded-t-xxl [&_#rightBody]:shadow-[0px_-5px_15px_0px_rgba(42,40,135,0.05)]',
        )}
        right={
          isStatusPath ? (
            <Outlet />
          ) : (
            <Suspense fallback={<DefaultLoader />}>
              <ScrollIntoView componentId="rightDualPanel" />
              <Outlet />
            </Suspense>
          )
        }
        left={
          isStatusPath ? null : (
            <Suspense fallback={<LeftBlockSkeleton />}>
              {renderLeftBlock()}
            </Suspense>
          )
        }
        rightHeader={
          <>
            {isMobileView ? null : (
              <ContainerLayout className="sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent">
                <MainHeader className="bg-transparent" />
              </ContainerLayout>
            )}
          </>
        }
        rightFooter={
          <ContainerLayout className="mt-auto bg-neutral-50 md:bg-transparent">
            <MainFooter />
          </ContainerLayout>
        }
      />
    </>
  );
};

function LeftBlockSkeleton() {
  return (
    <div className="md:size-full  h-[12.75rem]  -mb-8 md:mb-0">
      <Skeleton className="size-full" />
    </div>
  );
}
