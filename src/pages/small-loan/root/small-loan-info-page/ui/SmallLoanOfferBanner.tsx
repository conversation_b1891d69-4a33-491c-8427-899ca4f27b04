import {
  LOCIZE_CHECKOUT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN,
} from 'app-constants';
import { Typography } from 'components/typography';
import type { CarouselApi } from 'components/ui/carousel';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from 'components/ui/carousel';
import { useRootContext } from 'context/root';
import { useEffectOnce, useGetPageAttributes } from 'hooks';
import { useGetApplicationByReferenceSuspense } from 'hooks/use-get-application-by-reference-suspense';
import { useGetCurrentApplicationSuspense } from 'hooks/use-get-current-application-suspense';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { convertPageAttributeNamesToObject } from 'services/page-attributes-service';

export const SmallLoanOfferBanner = () => {
  const { user } = useRootContext();
  const [emblaApi, setEmblaApi] = useState<CarouselApi | null>(null);

  const { application } = (
    user
      ? useGetCurrentApplicationSuspense
      : useGetApplicationByReferenceSuspense
  )();

  const interestFreeMonths = application?.campaign?.interest_free_months ?? 0;

  const { t } = useTranslation(LocizeNamespaces.checkout);

  const { pageAttributes, getPageAttributes } = useGetPageAttributes();

  useEffectOnce(() => {
    getPageAttributes();
  });

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const specialOfferLocizeKeys = useMemo(() => {
    const offersKeys: string[] = [];

    if (
      visiblePageAttributes[
        PageAttributeNames.specialOfferContainerInterestFreeDisclaimer
      ]
    ) {
      if (interestFreeMonths <= 1) {
        offersKeys.push(
          SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_1_MONTH,
        );
      } else {
        offersKeys.push(
          SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.INTEREST_FREE_MANY_MONTHS,
        );
      }
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] &&
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      offersKeys.push(SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.VARIANT_1);
    }

    if (
      visiblePageAttributes[
        PageAttributeNames.fixedZeroContractFeeDisclaimer
      ] ||
      visiblePageAttributes[PageAttributeNames.fixedZeroManagementFeeDisclaimer]
    ) {
      offersKeys.push(SPECIAL_OFFER_LOCIZE_KEY_BY_CAMPAIGN.DEFAULT);
    }

    return offersKeys;
  }, [visiblePageAttributes, interestFreeMonths]);

  const autoplayIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!emblaApi || specialOfferLocizeKeys.length === 0) return;

    const scrollPrev = () => {
      emblaApi.scrollPrev();
    };

    if (autoplayIntervalRef.current) {
      clearInterval(autoplayIntervalRef.current);
    }

    autoplayIntervalRef.current = setInterval(scrollPrev, 5000);

    return () => {
      if (autoplayIntervalRef.current) {
        clearInterval(autoplayIntervalRef.current);
      }
    };
  }, [emblaApi, specialOfferLocizeKeys.length]); // Dependency array: re-run effect if emblaApi changes

  if (specialOfferLocizeKeys.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row justify-center bg-system-green-800 w-full h-[40px]">
      <Carousel
        opts={{
          loop: true,
          duration: 30,
        }}
        orientation="vertical"
        className="w-full"
        setApi={setEmblaApi}
      >
        <CarouselContent isInteractive className="">
          {specialOfferLocizeKeys.flatMap((offerKey, index) => [
            <CarouselItem key={`offer-${index}`}>
              <div className="flex flex-row gap-2 justify-center py-3 bg-system-green-800 w-full">
                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-system-yellow-400"
                >
                  {t(LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanLimitedOffer)}
                </Typography>

                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-white"
                >
                  {t(offerKey)}
                </Typography>
              </div>
            </CarouselItem>,

            <CarouselItem key={`apply-${index}`}>
              <div className="flex flex-row gap-2 justify-center py-3 bg-system-green-800 w-full">
                <Typography
                  variant="text-xs"
                  affects="semibold"
                  className="text-white"
                >
                  {t(
                    LOCIZE_CHECKOUT_TRANSLATION_KEYS.smallLoanLimitedOfferApplyNow,
                  )}
                </Typography>
              </div>
            </CarouselItem>,
          ])}
        </CarouselContent>
      </Carousel>
    </div>
  );
};
