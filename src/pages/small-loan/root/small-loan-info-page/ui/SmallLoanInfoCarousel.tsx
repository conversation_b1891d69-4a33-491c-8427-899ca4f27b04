import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from 'components/ui/carousel';

import { SMALL_LOAN_INFO_PAGE_FEATURES } from '../config';
import { SmallLoanInfoCard } from './SmallLoanInfoCard';

export const SmallLoanInfoCarousel = () => {
  return (
    <div className="w-full px-6 py-8 bg-black h-[12.75rem] -mb-8">
      <Carousel className="w-full" opts={{ align: 'start', dragFree: true }}>
        <CarouselContent isInteractive className="">
          {SMALL_LOAN_INFO_PAGE_FEATURES.map((feature, index) => {
            const { icon, title, description } = feature;
            return (
              <CarouselItem className="basis-[16.625rem]" key={index}>
                <SmallLoanInfoCard
                  icon={icon}
                  title={title}
                  description={description}
                />
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </div>
  );
};
