import { LocizeNamespaces } from 'app-constants';
import { Typography } from 'components/typography';
import { useTranslation } from 'react-i18next';

type SmallLoanInfoCardProps = {
  icon: React.ElementType;
  title: string;
  description: string;
};

export const SmallLoanInfoCard = ({
  icon: FeatureIcon,
  title,
  description,
}: SmallLoanInfoCardProps) => {
  const { t } = useTranslation(LocizeNamespaces.checkout);

  return (
    <div className="flex flex-col gap-2">
      <FeatureIcon className="mb-4" />
      <Typography variant="text-l" affects="bold" className="text-white">
        {t(title)}
      </Typography>
      <Typography variant="text-s" className="text-neutral-300">
        {t(description)}
      </Typography>
    </div>
  );
};
