import { zodResolver } from '@hookform/resolvers/zod';
import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_LOGIN_TRANSLATION_KEYS,
  LocizeNamespaces,
  LoginPageViewTypes,
  STEP_COUNTER_COMPONENT_ID,
} from 'app-constants';
import { FormInputField } from 'components/form/form-input-field';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useLoginPageContext } from 'context/small-loan';
import { usePayseraEmailLogin } from 'hooks';
import { DisplayStyles } from 'models';
import { useLayoutEffect } from 'react';
import type { FieldValues } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { extractValidationErrors, setElementDisplayById } from 'services';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import * as z from 'zod';

import styles from './Login.module.scss';

const MagicLinkFormSchema = z.object({
  email: z.string().email(),
});

type MagicLinkFormType = z.infer<typeof MagicLinkFormSchema>;

export const MagicLinkView = () => {
  const { t } = useTranslation(LocizeNamespaces.login);
  const { t: tr } = useTranslation(LocizeNamespaces.common);
  const { setPageViewType } = useLoginPageContext();

  const form = useForm<MagicLinkFormType>({
    resolver: zodResolver(MagicLinkFormSchema),
    defaultValues: {
      email: '',
    },
  });

  const { payseraEmailLogin, payseraEmailLoginError } = usePayseraEmailLogin();

  const loginValidationErrors = extractValidationErrors(payseraEmailLoginError);

  useLayoutEffect(() => {
    setElementDisplayById(STEP_COUNTER_COMPONENT_ID, DisplayStyles.none);
    return () => {
      setElementDisplayById(STEP_COUNTER_COMPONENT_ID, DisplayStyles.flex);
    };
  });

  const onMagicLinkFormSubmit = async ({ email }: FieldValues) => {
    try {
      await payseraEmailLogin(email);
      setPageViewType(LoginPageViewTypes.pending);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  return (
    <div className={styles['magic-link-view-container']}>
      <p className={styles.paragraph}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.loginConfirmationProcessingDisclaimer)}
      </p>
      <p className={styles.paragraph}>
        {t(
          LOCIZE_LOGIN_TRANSLATION_KEYS.customerAwaitingEmailAddressDisclaimer,
        )}
      </p>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onMagicLinkFormSubmit)}
          className="grid w-full gap-2 mt-7"
        >
          <FormInputField
            control={form.control}
            name={FormFieldNames.email}
            label={t(LOCIZE_LOGIN_TRANSLATION_KEYS.emailFieldLabel)}
            invalid={
              loginValidationErrors[FormFieldNames.email] ||
              !!form.getFieldState(FormFieldNames.email).error
            }
            disabled={form.formState.isSubmitting}
          />

          <Button loading={form.formState.isSubmitting} type="submit">
            {tr(LOCIZE_COMMON_TRANSLATION_KEYS.submit)}
          </Button>
        </form>
      </Form>
      <p className={styles.paragraph}>
        {t(LOCIZE_LOGIN_TRANSLATION_KEYS.customerAwaitingPatienceDisclaimer)}
      </p>
    </div>
  );
};
