import {
  LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

export const InsuranceInfoHeader = ({ className }: { className?: string }) => {
  const { t } = useTranslation(LocizeNamespaces.incomeInsurance);
  const [isRedirecting, setIsRedirecting] = useState(false);

  const onLearnMoreClick = () => {
    setIsRedirecting(true);
    window.open(REDIRECT_URLS.incomeInsurance, '_blank');
    setTimeout(() => {
      setIsRedirecting(false);
    }, 300);
  };

  return (
    <div className={cn('w-full', className)}>
      <div className="w-full flex justify-between gap-4 flex-row items-center max-w-100">
        <Typography variant="xs" affects="medium">
          {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.bannerTitle)}
        </Typography>
        <Button
          variant="green"
          size="small"
          loading={isRedirecting}
          onClick={onLearnMoreClick}
        >
          {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.ctaButtonLabel)}
        </Button>
      </div>
    </div>
  );
};
