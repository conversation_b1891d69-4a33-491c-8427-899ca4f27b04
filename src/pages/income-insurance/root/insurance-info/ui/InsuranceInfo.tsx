import {
  LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppLocalizationComponent } from 'components';
import { Typography } from 'components/typography';
import InsuranceBanner from 'img/insurance-banner.webp';
import { useTranslation } from 'react-i18next';
import { ContainerLayout } from 'widgets/layouts/container-layout';

import { useGetInsuranceInfo } from '../hooks';
import { InsuranceInfoHeader } from './InsuranceInfoHeader';

export const InsuranceInfo = () => {
  const { t } = useTranslation(LocizeNamespaces.incomeInsurance);

  const { maxInsuranceRepaymentPeriod, maxInsuranceRepaymentAmount } =
    useGetInsuranceInfo();

  return (
    <ContainerLayout className="py-6" noXPadding>
      <InsuranceInfoHeader />
      <div
        style={{
          backgroundImage: `url(${InsuranceBanner})`,
        }}
        className="bg-cover bg-position-[0%_30%] h-67 w-full rounded-2xl max-w-100 relative xs:h-[25rem] mt-6 md:bg-center md:bg-no-repeat"
      >
        <div
          className="absolute w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='3' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%' height='100%' filter='url(%23noise)' opacity='0.45'/%3E%3C/svg%3E")`,
            mixBlendMode: 'overlay',
          }}
        />
      </div>
      <Typography
        variant="text-l"
        affects="medium"
        tag="div"
        className="text-center mt-6"
      >
        <AppLocalizationComponent
          values={{
            maxInsuranceRepaymentPeriod,
            maxInsuranceRepaymentAmount,
          }}
          locizeKey={LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.bannerDescription}
          t={t}
        />
      </Typography>
    </ContainerLayout>
  );
};
