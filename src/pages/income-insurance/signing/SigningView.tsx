import { UsersignInMethod } from 'api/core/generated';
import {
  AppLanguages,
  FormFieldNames,
  LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS,
  LOCIZE_SIGNING_TRANSLATION_KEYS,
  LocizeNamespaces,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormInputField } from 'components/form/form-input-field';
import { FormPhoneField } from 'components/form/form-phone-field';
import { FormRangeInputField } from 'components/form/form-range-input-field/FormRangeInputField';
import { LogoutButton } from 'components/logout-button';
import { Notification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { useIncomeSigningPageContext } from 'context/income-insurance';
import { onlyPasswordSigningEnabled } from 'environment';
import type { SigningPageFormType } from 'hooks/page-logic/income-insurance';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isAppLanguage } from 'utils';

import { PayseraSigningMethodDialog } from './PayseraSigningMethodDialog';

export const SigningView = () => {
  const { t, i18n } = useTranslation(LocizeNamespaces.incomeInsurance);
  const {
    form,
    onFormSubmit,
    insuranceProviderOptions,
    shouldShowEmailPhoneFields,
    signAppWithBanklink,
    userCanSignContract,
    banklinkOptions,
    processingSigning,
    signInMethod,
    userUpdateErrors,
  } = useIncomeSigningPageContext();

  const [
    isPayseraSigningMethodDialogOpen,
    setIsPayseraSigningMethodDialogOpen,
  ] = useState(false);

  const selectedInsuranceOptionIndex = form.watch(
    FormFieldNames.insuranceOption,
  );

  const selectedInsuranceOption =
    insuranceProviderOptions?.[selectedInsuranceOptionIndex];

  const isBanklinkSigningAllowed =
    !onlyPasswordSigningEnabled &&
    (signInMethod === UsersignInMethod.PAYSERA_BANKLINK ||
      signInMethod === UsersignInMethod.MAGIC_LINK);

  const isFieldDisabled = form.formState.isSubmitting || processingSigning;

  return (
    <Form {...form}>
      <form
        className="w-full"
        onSubmit={form.handleSubmit(
          onFormSubmit({
            onBanklinkSigning: () => {
              setIsPayseraSigningMethodDialogOpen(true);
            },
          }),
        )}
      >
        <Typography variant="text-l" affects="bold">
          {t(LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing.rangeLabel)}
        </Typography>

        <FormRangeInputField<SigningPageFormType>
          className="mt-6 mb-10"
          control={form.control}
          name={FormFieldNames.insuranceOption}
          min={0}
          max={Number(insuranceProviderOptions?.length) - 1}
          step={1}
          loading={!insuranceProviderOptions?.length}
          disabled={isFieldDisabled}
          customValueOutput={
            <div className="flex items-baseline gap-2">
              <Typography tag="div" variant="m" className="text-[2rem]">
                {`${selectedInsuranceOption?.insured_amount} €`}
              </Typography>
              <Typography className="text-neutral-500 -translate-y-[.1rem]">
                {t(
                  LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing
                    .perMonthLabel,
                )}
              </Typography>
            </div>
          }
        />

        <div className="flex justify-between -mt-4">
          <Typography variant="text-m" className="text-neutral-500">
            {t(
              LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing.monthlyCostLabel,
            )}
          </Typography>
          <Typography>
            {`${selectedInsuranceOption?.total_monthly_premium_amount} € ${t(
              LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing.perMonthLabel,
            )}`}
          </Typography>
        </div>

        {shouldShowEmailPhoneFields && (
          <div className="mt-10 space-y-4">
            <FormPhoneField<SigningPageFormType>
              name={FormFieldNames.phone}
              control={form.control}
              label={t(
                LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing.phoneLabel,
              )}
              disabled={isFieldDisabled}
              invalid={userUpdateErrors[FormFieldNames.phone]}
            />
            <FormInputField
              control={form.control}
              name={FormFieldNames.email}
              label={t(
                LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing.emailLabel,
              )}
              disabled={isFieldDisabled}
              invalid={userUpdateErrors[FormFieldNames.email]}
            />
          </div>
        )}

        <FormCheckboxField<SigningPageFormType>
          containerClassName="mt-2.5 mt-10"
          name={FormFieldNames.insuranceTermsOfService}
          control={form.control}
          label={
            <AppLocalizationComponent
              t={t}
              components={{
                termsLink: (
                  <AppExternalLink
                    className="underline cursor-pointer hover:text-primary-brand-02"
                    to={REDIRECT_URLS.incomeInsuranceTerms.replace(
                      ':locale',
                      i18n.language,
                    )}
                  />
                ),
              }}
              locizeKey={
                LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing
                  .termsOfServiceCheckboxLabel
              }
            />
          }
          disabled={isFieldDisabled}
        />

        <FormCheckboxField<SigningPageFormType>
          containerClassName="mt-2.5 mt-4"
          name={FormFieldNames.insuranceDataPolicy}
          control={form.control}
          label={
            <AppLocalizationComponent
              t={t}
              components={{
                policyLink: (
                  <AppExternalLink
                    className="underline cursor-pointer hover:text-primary-brand-02"
                    to={
                      REDIRECT_URLS.incomeInsuranceDataPolicy[
                        isAppLanguage(i18n.language)
                          ? i18n.language
                          : AppLanguages.en
                      ]
                    }
                  />
                ),
              }}
              locizeKey={
                LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing
                  .dataPolicyCheckboxLabel
              }
            />
          }
          disabled={isFieldDisabled}
        />

        <FormCheckboxField<SigningPageFormType>
          containerClassName="mt-2.5 mt-4"
          name={FormFieldNames.insuranceHealthAndEmployment}
          control={form.control}
          label={t(
            LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing
              .healthAndEmploymentCheckboxLabel,
          )}
          disabled={isFieldDisabled}
        />

        <div className="mt-12">
          {userCanSignContract ? (
            <Button
              fullWidth
              variant="black"
              disabled={!form.formState.isValid}
              loading={isFieldDisabled}
              type="submit"
            >
              {t(
                LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS.signing
                  .signButtonLabel,
              )}
            </Button>
          ) : (
            <div className="flex flex-col gap-6">
              <Notification>
                {t(LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodDisclaimer)}
              </Notification>
              <LogoutButton variant="black" fullWidth>
                {t(
                  LOCIZE_SIGNING_TRANSLATION_KEYS.changeLoginMethodButtonLabel,
                )}
              </LogoutButton>
            </div>
          )}
        </div>

        {isBanklinkSigningAllowed && (
          <PayseraSigningMethodDialog
            isOpen={isPayseraSigningMethodDialogOpen}
            onOpenChange={setIsPayseraSigningMethodDialogOpen}
            banklinkOptions={banklinkOptions}
            onConfirm={(payment_method_key) => {
              signAppWithBanklink?.(payment_method_key);
            }}
            disabled={processingSigning}
            isConfirming={processingSigning}
          />
        )}
      </form>
    </Form>
  );
};
