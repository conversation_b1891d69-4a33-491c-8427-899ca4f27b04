import {
  AccountScoringViewType,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { AppExternalLink } from 'components/app-external-link';
import { AppLocalizationComponent } from 'components/app-localization-component';
import { CircleLoader } from 'components/circle-loader';
import { Dialog as AppDialog } from 'components/dialog';
import { AutoNotification } from 'components/notification';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Card } from 'components/ui/card';
import { useRootContext } from 'context/root';
import { useAccountScoringPageLogic } from 'hooks/page-logic/credit-line';
import AccountScoringLogoIcon from 'icons/account-scoring-logo.svg?react';
import { Plus } from 'lucide-react';
import { lazy, Suspense, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const ScoringMethodsDialog = lazy(() =>
  import('./ScoringMethodsDialog').then((module) => ({
    default: module.ScoringMethodsDialog,
  })),
);

const Page = () => {
  const {
    onAccountStatementUploaded,
    onAccountScoringRedirectButtonClick,
    isRedirectingToEmta,
    uploadAccountStatementProcessing,
    onEmtaRedirectButtonClick,
    isRedirectingToAccountScoring,
    accountScoringButtonDisabled,
    accountScoringViewType,
    accountScoringUrl,
    goBackToScoringMethods,
    isEmtaButtonDisabled,
  } = useAccountScoringPageLogic();
  const { pageUrlAndNavigationProcessing, getPageUrlAndNavigate } =
    useRootContext();

  const { t } = useTranslation(LocizeNamespaces.accountScoringV2);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  if (accountScoringViewType === AccountScoringViewType.SCORING) {
    return (
      <div>
        <FullScreenLayout>
          <CircleLoader />

          <AutoNotification delay={10000} isAutoAppear className="mb-10">
            <AppLocalizationComponent
              components={{
                button: (
                  <button
                    type="button"
                    onClick={() => {
                      goBackToScoringMethods();
                      setIsDialogOpen(true);
                    }}
                    className="text-primary-brand-02 underline hover:text-primary-brand-02"
                  />
                ),
                external_link: (
                  <AppExternalLink
                    className={cn(
                      'text-primary-brand-02 underline hover:text-primary-brand-02',
                      accountScoringUrl
                        ? 'cursor-pointer'
                        : 'pointer-events-none',
                    )}
                    to={accountScoringUrl ?? ''}
                  />
                ),
              }}
              locizeKey={
                LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.problemsVerifyingWithAccountScoringDisclaimer
              }
              t={t}
            />
          </AutoNotification>

          <Typography variant="xs" className="text-center">
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.verifyingFinancialInfo)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.keepPageOpenDisclaimer)}
          </Typography>

          <div className="flex flex-col gap-4 mt-12">
            <Button
              fullWidth
              variant="white"
              loading={pageUrlAndNavigationProcessing}
              onClick={() => setIsConfirmDialogOpen(true)}
            >
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
            </Button>
          </div>
        </FullScreenLayout>

        <AppDialog
          title={t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.confirmTitle)}
          open={isConfirmDialogOpen}
          onOpenChange={setIsConfirmDialogOpen}
        >
          <Typography>
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.confirmDescription)}
          </Typography>
          <Button
            className="mt-12"
            variant="black"
            onClick={() => {
              goBackToScoringMethods();
              setIsConfirmDialogOpen(false);
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.confirm)}
          </Button>
        </AppDialog>
      </div>
    );
  }
  if (accountScoringViewType === AccountScoringViewType.MANUAL_UPLOAD_SCORING) {
    return (
      <div>
        <FullScreenLayout>
          <CircleLoader />

          <Typography variant="xs" className="text-center">
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.verifyingFinancialInfo)}
          </Typography>
          <Typography className="mt-4 text-center">
            {t(
              LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.manualUploadPendingDisclaimer,
            )}
          </Typography>

          <div className="flex flex-col gap-4 mt-12 w-full">
            <Button
              fullWidth
              variant="black"
              loading={pageUrlAndNavigationProcessing}
              onClick={() => {
                goBackToScoringMethods();
                setIsDialogOpen(true);
              }}
            >
              {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsButton)}
            </Button>

            <Button
              fullWidth
              variant="white"
              loading={pageUrlAndNavigationProcessing}
              onClick={() => setIsConfirmDialogOpen(true)}
            >
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
            </Button>
          </div>
        </FullScreenLayout>

        <AppDialog
          title={t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.confirmTitle)}
          open={isConfirmDialogOpen}
          onOpenChange={setIsConfirmDialogOpen}
        >
          <Typography>
            {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.confirmDescription)}
          </Typography>
          <Button
            className="mt-12"
            variant="black"
            onClick={() => {
              goBackToScoringMethods();
              setIsConfirmDialogOpen(false);
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.confirm)}
          </Button>
        </AppDialog>
      </div>
    );
  }

  return (
    <div>
      <Typography variant="xxs">
        {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.verificationMethodTitle)}
      </Typography>

      <Card className="mt-6 p-6">
        <div className="flex items-start space-x-4">
          <AccountScoringLogoIcon />
          <div className="flex flex-col">
            <Typography variant="xs">
              {t(
                LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.accountScoringMethodTitle,
              )}
            </Typography>
            <Typography
              className="text-primary-brand-02"
              variant="text-s"
              affects="medium"
            >
              {t(
                LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.accountScoringMethodRecommended,
              )}
            </Typography>
          </div>
        </div>
        <Typography className="mt-6" variant="text-s">
          {t(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.accountScoringMethodDescription,
          )}
        </Typography>
        <Button
          fullWidth
          className="mt-10"
          onClick={onAccountScoringRedirectButtonClick}
          disabled={accountScoringButtonDisabled}
          loading={isRedirectingToAccountScoring}
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>
      </Card>

      <Card
        className="mt-4 flex cursor-pointer items-center justify-between p-4"
        onClick={() => setIsDialogOpen(true)}
      >
        <Typography>
          {t(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsButton)}
        </Typography>
        <Plus className="h-4 w-4 rounded-full border border-primary-black" />
      </Card>

      <Button
        fullWidth
        className="mt-12"
        variant="white"
        loading={pageUrlAndNavigationProcessing}
        onClick={() => {
          getPageUrlAndNavigate(false);
        }}
      >
        {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
      </Button>

      <Suspense fallback={null}>
        <ScoringMethodsDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          isRedirectingToEmta={isRedirectingToEmta}
          isEmtaButtonDisabled={isEmtaButtonDisabled}
          uploadAccountStatementProcessing={uploadAccountStatementProcessing}
          onEmtaRedirectButtonClick={onEmtaRedirectButtonClick}
          onAccountStatementUploaded={onAccountStatementUploaded}
        />
      </Suspense>
    </div>
  );
};

export default Page;
