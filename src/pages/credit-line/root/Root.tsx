import { CreditLineRoutePaths } from 'app-constants';
import { DefaultLoader } from 'components/default-loader/DefaultLoader';
import { CreditLineProgressBar } from 'components/progress-bar/credit-line-progress-bar';
import { ScrollIntoView } from 'components/scroll-into-view';
import { useRootContext } from 'context/root';
import { useCheckIsEqualCurrentPageUrl, usePageUrlSuspenseState } from 'hooks';
import { useIsMobileView } from 'hooks/system';
import { lazy, Suspense } from 'react';
import Skeleton from 'react-loading-skeleton';
import { Outlet, useLocation } from 'react-router-dom';
import { cn } from 'utils/tailwind';
import { MainFooter } from 'widgets/footers/main-footer';
import { MainHeader } from 'widgets/headers/main-header';
import { ContainerLayout } from 'widgets/layouts/container-layout';
import { PageLayout } from 'widgets/layouts/page-layout/PageLayout';

import { CreditLineWithMobileHeaderRoutePaths } from './config';
import { CreditAccountManager } from './CreditAccountManager';

const CreditLineInfoHeader = lazy(() =>
  import('./credit-line-info/ui/CreditLineInfoHeader').then((module) => ({
    default: module.CreditLineInfoHeader,
  })),
);

const ApprovedCreditLimitBlock = lazy(() =>
  import('./approved-credit-limit-block/ApprovedCreditLimitBlock').then(
    (module) => ({ default: module.ApprovedCreditLimitBlock }),
  ),
);

const CreditLineInfo = lazy(() =>
  import('./credit-line-info/ui/CreditLineInfo').then((module) => ({
    default: module.CreditLineInfo,
  })),
);

export const Root = () => {
  const { pathname } = useLocation();
  const isMobileView = useIsMobileView();
  const { user } = useRootContext();
  const isLoginPath = pathname.includes(CreditLineRoutePaths.LOGIN);
  const isCheckoutPath = pathname.includes(CreditLineRoutePaths.CHECKOUT);
  const isSigningUpPath = pathname.includes(CreditLineRoutePaths.SIGNING);
  const isSpouseConsentPath = pathname.includes(CreditLineRoutePaths.SPOUSE);
  const isStatusPath =
    pathname.includes(CreditLineRoutePaths.REJECT) ||
    pathname.includes(CreditLineRoutePaths.PENDING) ||
    pathname.includes(CreditLineRoutePaths.SUCCESS);

  const [, , pagePath] = location.pathname.split('/');

  const { loading, data } = usePageUrlSuspenseState();

  const isEqualCurrentPageUrl = useCheckIsEqualCurrentPageUrl();

  const isMobileFullView =
    isMobileView &&
    CreditLineWithMobileHeaderRoutePaths.includes(
      pagePath as CreditLineRoutePaths,
    );

  const renderLeftBlock = () => {
    const isContentReady =
      isSpouseConsentPath ||
      (data?.page_url && !loading) ||
      isEqualCurrentPageUrl;

    if (!isContentReady) {
      return <LeftBlockSkeleton />;
    }

    if (user?.id && isSigningUpPath) {
      return <ApprovedCreditLimitBlock />;
    }

    if (isMobileFullView) {
      return null;
    }

    return <CreditLineInfo />;
  };

  return (
    <>
      <PageLayout
        className={cn(
          'font-family-inter',
          isMobileView &&
            (isCheckoutPath || isLoginPath || isSigningUpPath) &&
            '[&_#rightBody]:border-t [&_#rightBody]:border-solid [&_#rightBody]:border-neutral-200 [&_#rightBody]:rounded-t-xxl [&_#rightBody]:shadow-[0px_-5px_15px_0px_rgba(42,40,135,0.05)]',
        )}
        right={
          isStatusPath ? (
            <CreditAccountManager>
              <Outlet />
            </CreditAccountManager>
          ) : (
            <Suspense fallback={<DefaultLoader />}>
              <CreditAccountManager>
                <ScrollIntoView componentId="rightDualPanel" />
                <Outlet />
              </CreditAccountManager>
            </Suspense>
          )
        }
        left={
          isStatusPath ? null : (
            <Suspense fallback={<LeftBlockSkeleton />}>
              {renderLeftBlock()}
            </Suspense>
          )
        }
        leftHeader={
          isMobileView ? null : (
            <ContainerLayout className="sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent">
              <MainHeader className="bg-transparent" />
            </ContainerLayout>
          )
        }
        rightHeader={
          <>
            {isMobileFullView && (
              <CreditLineInfoHeader classNames="px-[1.5rem] flex justify-center md:px-[2rem] pt-8" />
            )}
            <CreditLineProgressBar />
          </>
        }
        leftFooter={
          isMobileView ? null : (
            <ContainerLayout className="mt-auto ">
              <MainFooter />
            </ContainerLayout>
          )
        }
        rightFooter={
          isMobileView ? (
            <ContainerLayout className="mt-auto ">
              <MainFooter />
            </ContainerLayout>
          ) : null
        }
      />
    </>
  );
};

function LeftBlockSkeleton() {
  return (
    <div className="w-screen h-[27rem] flex flex-col gap-4 mt-4 mb-[5.25rem] max-w-[18rem] md:mt-0 md:mb-0 md:max-w-[22rem]">
      <Skeleton className="w-full h-[5rem] rounded-2xl" />
      <Skeleton className="w-full h-[20rem] rounded-2xl" />
    </div>
  );
}
