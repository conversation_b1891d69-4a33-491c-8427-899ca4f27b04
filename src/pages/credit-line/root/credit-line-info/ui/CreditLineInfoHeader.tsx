import {
  LOCIZE_CREDIT_LINE_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { CreditLineInfoDialog } from 'pages/credit-line/root';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

export const CreditLineInfoHeader = ({
  classNames,
}: {
  classNames?: string;
}) => {
  const { t } = useTranslation(LocizeNamespaces.creditLine);

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  return (
    <div className={cn(classNames)}>
      <div className="w-full flex justify-between gap-4 flex-row items-center max-w-100">
        <CreditLineInfoDialog
          isOpen={isDialogOpen}
          onOpenChange={setIsDialogOpen}
        />
        <Typography variant="xs" affects="medium">
          {t(LOCIZE_CREDIT_LINE_TRANSLATION_KEYS.title)}
        </Typography>
        <Button
          variant="green"
          size="small"
          onClick={() => setIsDialogOpen(true)}
        >
          {t(LOCIZE_CREDIT_LINE_TRANSLATION_KEYS.learnMoreButton)}
        </Button>
      </div>
    </div>
  );
};
