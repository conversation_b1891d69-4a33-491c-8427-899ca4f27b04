import {
  AppOnboardingCampaigns,
  AppSearchParams,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CREDIT_LINE_TRANSLATION_KEYS,
  LOCIZE_SUCCESS_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { CampaignBanner } from 'components/campaign-banner';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useRootContext } from 'context/root';
import { useSuccessPageLogic } from 'hooks/page-logic/credit-line';
import SuccessIcon from 'icons/success.svg?react';
import { lazy, Suspense } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';
import { useSearchParams } from 'react-router-dom';
import { formatNumber } from 'utils/formatNumber';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const NewsLetterAgreementSectionLazy = lazy(() =>
  import('./NewsletterAgreementSection').then((module) => ({
    default: module.NewsLetterAgreementSection,
  })),
);

const Page = () => {
  const [searchParams] = useSearchParams();

  const { t } = useTranslation(LocizeNamespaces.success);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const { t: tcl } = useTranslation(LocizeNamespaces.creditLine);
  const { user } = useRootContext();

  const {
    onEstoAccountButtonClick,
    onWithdrawButtonClick,
    isRedirectingToCustomerProfile,
    isRedirectingToCreditAccountWithdrawal,
  } = useSuccessPageLogic();

  const isInterestFreeOnboarding =
    searchParams.get(AppSearchParams.onboarding) ===
    AppOnboardingCampaigns.creditLineInterestFree;

  return (
    <FullScreenLayout>
      <SuccessIcon />
      <Typography className="mt-6 text-center" variant="m">
        {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.successLabel)}
      </Typography>
      <Typography className="mt-4 text-center">
        {
          <Trans
            t={t}
            i18nKey={LOCIZE_SUCCESS_TRANSLATION_KEYS.creditLineDisclaimer}
            values={{
              creditLimit: formatNumber({
                value: user?.credit_accounts?.[0]?.credit_limit,
              }),
            }}
          />
        }
      </Typography>

      {isInterestFreeOnboarding ? (
        <CampaignBanner
          className="mt-6 w-full"
          title={tcl(LOCIZE_CREDIT_LINE_TRANSLATION_KEYS.campaignBannerTitle)}
          extraInfo={tc(
            LOCIZE_COMMON_TRANSLATION_KEYS.interestFreeCampaignInfo,
          )}
        />
      ) : null}

      {!user?.newsletter_agreement ? (
        <Suspense
          fallback={
            <div className="w-full">
              <Skeleton className="animate-pulse-opacity h-37.75 w-full rounded-2xl mt-4" />
            </div>
          }
        >
          <NewsLetterAgreementSectionLazy className="mt-4" />
        </Suspense>
      ) : null}

      <Button
        className={cn('mt-12', !user?.newsletter_agreement && 'mt-8')}
        fullWidth
        onClick={onWithdrawButtonClick}
        loading={isRedirectingToCreditAccountWithdrawal}
      >
        {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.withdrawButtonLabel)}
      </Button>

      <Button
        className="mt-4"
        fullWidth
        onClick={onEstoAccountButtonClick}
        loading={isRedirectingToCustomerProfile}
        variant="transparent"
      >
        {t(LOCIZE_SUCCESS_TRANSLATION_KEYS.estoAccountButtonLabel)}
      </Button>
    </FullScreenLayout>
  );
};

export default Page;
