import {
  AppLanguages,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_REJECT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
  REDIRECT_URLS,
} from 'app-constants';
import { AppExternalLink, AppLocalizationComponent } from 'components';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { useRejectPageLogic } from 'hooks/page-logic/credit-line';
import CloseIcon from 'icons/close-cancel.svg?react';
import credy from 'img/credy.png';
import type { AnyObject } from 'models';
import { Trans, useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';
import { FullScreenLayout } from 'widgets/layouts/full-screen-layout';

const Page = () => {
  const { t } = useTranslation(LocizeNamespaces.reject);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  const {
    visiblePageAttributes,
    onGoToFAQButtonClick,
    onEstoAccountButtonClick,
    onForwardLoanClick,
    isRedirectingToCustomerProfile,
    isForwardLoadLinkProcessing,
  } = useRejectPageLogic();

  const shouldShowButtonsSection = !(
    !visiblePageAttributes[
      PageAttributeNames.frequentlyAskedQuestionsSection
    ] && !visiblePageAttributes[PageAttributeNames.returnToMerchantButton]
  );

  return (
    <FullScreenLayout>
      {visiblePageAttributes[PageAttributeNames.forwardRejectsSection] ? (
        <>
          <div className="w-28 mb-6">
            <img alt="Credy" src={credy} />
          </div>
          <div className="text-center">
            <Typography variant="xs" className="text-center mb-4">
              {t(
                LOCIZE_REJECT_TRANSLATION_KEYS.creditLineRejectionWithCredyHeading,
              )}
            </Typography>
            <Trans
              i18nKey={
                LOCIZE_REJECT_TRANSLATION_KEYS.creditLineRejectionWithCredyDisclaimer
              }
              t={t}
            />
            <Button
              loading={isForwardLoadLinkProcessing}
              className="mt-12"
              fullWidth
              variant="black"
              onClick={onForwardLoanClick}
            >
              {t(
                LOCIZE_REJECT_TRANSLATION_KEYS.creditLineRejectionWithCredyButton,
              )}
            </Button>

            <AppLocalizationComponent
              className={'text-neutral-500 mt-4 text-sm'}
              components={{
                site_link: (
                  <AppExternalLink
                    to={
                      (REDIRECT_URLS.credyTermsPageUrs as AnyObject)[
                        AppLanguages.lt
                      ]
                    }
                  />
                ),
              }}
              locizeKey={LOCIZE_REJECT_TRANSLATION_KEYS.credyDisclaimer}
              t={t}
            />
          </div>
        </>
      ) : (
        <>
          <div className=" flex items-center justify-center rounded-full w-14 h-14 bg-neutral-200 mb-6">
            <CloseIcon className="rounded-full" />
          </div>
          <Typography variant="xs" className="text-center">
            {t(
              LOCIZE_REJECT_TRANSLATION_KEYS.creditLineDefaultRejectionDisclaimer,
            )}
          </Typography>

          <Typography className="mt-4 text-center">
            {t(
              LOCIZE_REJECT_TRANSLATION_KEYS.creditLineDefaultRejectionDisclaimer2,
            )}
          </Typography>
        </>
      )}

      {shouldShowButtonsSection ? (
        <div className="w-full flex flex-col gap-4 mt-12">
          {visiblePageAttributes[
            PageAttributeNames.frequentlyAskedQuestionsSection
          ] ? (
            <Button
              className={cn(
                visiblePageAttributes[
                  PageAttributeNames.forwardRejectsSection
                ] && 'underline',
              )}
              onClick={onGoToFAQButtonClick}
              variant={
                visiblePageAttributes[PageAttributeNames.forwardRejectsSection]
                  ? 'white'
                  : 'black'
              }
              fullWidth
            >
              {t(LOCIZE_REJECT_TRANSLATION_KEYS.creditLineRejectionReasons)}
            </Button>
          ) : null}

          {visiblePageAttributes[PageAttributeNames.estoAccountSection] ? (
            <Button
              loading={isRedirectingToCustomerProfile}
              onClick={onEstoAccountButtonClick}
              variant="grey"
              fullWidth
            >
              {tc(LOCIZE_COMMON_TRANSLATION_KEYS.goToEstoAccountButton)}
            </Button>
          ) : null}
        </div>
      ) : null}
    </FullScreenLayout>
  );
};

export default Page;
