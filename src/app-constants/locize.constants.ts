import { AppLoginMethods } from './app.constants';

export const LOCIZE_LANGUAGE_DETECTION_PLACES = {
  localStorage: 'localStorage',
  queryString: 'querystring',
};

export enum LocizeNamespaces {
  pageTitles = 'page-titles',
  common = 'common',
  offer = 'offer',
  success = 'success',
  reject = 'reject',
  signing = 'signing',
  pending = 'pending',
  contact = 'contact',
  purposeOfLoan = 'purpose-of-loan',
  contactExtra = 'contact-extra',
  errors = 'errors',
  error = 'error',
  login = 'login',
  checkout = 'checkout',
  accountScoring = 'account-scoring',
  accountScoringV2 = 'account-scoring-v2',
  user = 'user',
  spouseConsent = 'spouse-consent',
  emtaConsent = 'emta-consent',
  emtaConsentV2 = 'emta-consent-v2',
  schedule = 'schedule',
  afterDirectPayment = 'after-direct-payment',
  creditLine = 'credit-line',
  incomeVerification = 'income-verification',
  incomeInsurance = 'income-insurance',
}

export const LOCIZE_PAGE_TITLES_KEYS = {
  pageTitleSmallLoan: 'page-title-small-loan',
  pageTitleFastLoan: 'page-title-fast-loan',
  pageTitleRenovationLoan: 'page-title-renovation-loan',
  pageTitleVehicleLoan: 'page-title-vehicle-loan',
  pageTitleTravelLoan: 'page-title-travel-loan',
  pageTitleHealthLoan: 'page-title-health-loan',
  pageTitleBeautyLoan: 'page-title-beauty-loan',
  pageTitleHP: 'page-title-hire-purchase',
  pageTitleFreeHP: 'page-title-free-hire-purchase',
  pageTitleEstoPay: 'page-title-esto-pay',
  pageTitleEstoPaySuccess: 'page-title-esto-pay-success',
  pageTitleEstoPayFailure: 'page-title-esto-pay-failure',
  pageTitleCL: 'page-title-credit-line',
  pageTitleIncomeInsurance: 'page-title-income-insurance',
};

export const LOCIZE_ERRORS_TRANSLATION_KEYS = {
  errorCode_1201: '1201', // Mobile authentication was not successful
  errorCode_1202: '1202', // Mobile signing was not successful
  errorCode_1203: '1203', // SSL certificate verification failure
  errorCode_1204: '1204', // The provided authentication certificate or issuer is not valid
  errorCode_1205: '1205', // Wrong username or password
  errorCode_1206: '1206', // Signing by ID card was not successful
  errorCode_1208: '1208', // Smart ID authentication was not successful
  errorCode_1214: '1214', // You are using the basic version of Smart-ID
  errorCode_1216: '1216', // The entered personal ID number is not correct
  errorCode_1217: '1217', // You do not have the Mobile-ID service activated or the certificates are not valid
  errorCode_1219: '1219', // A technical error occurred
  errorCode_1220: '1220', // Mobile-ID authentication was canceled from the phone
  errorCode_1221: '1221', // A technical error occurred
  errorCode_1222: '1222', // Cannot establish a connection with your phone
  errorCode_1223: '1223', // Your phone's SIM card's settings do not match with the Mobile-ID service
  errorCode_1304: '1304', // This application couldn't be retrieved
  errorCode_1305: '1305', // The purchase amount is not within the allowed range
  errorCode_1502: '1502', // The new password and password confirmation do not match
  errorCode_1503: '1503', // The new password is too short
  errorCode_1507: '1507', // The document number is not correct
  errorCode_2401: '2401', // We apologize, but the service is currently unavailable
  errorCode_2804: '2804', // Unfortunately you cannot withdraw credit
  errorCode_2805: '2805', // Withdrawals cannot be made at this time
  generalError: 'general', // Something went wrong!
  validationError: 'validation', // Please fill out all required fields
  validationErrorDocuments_1: 'validation-error.documents-1',
  validationErrorDocuments_2: 'validation-error.documents.2',
  uploadFileQuantity: 'upload-file-quantity',
  uploadFileFormat: 'upload-file-format',
  uploadFileMaxSize: 'upload-file-max-size',
  authenticationErrorLabel: 'authentication-error-label',
  idCardLoginError: 'id-card-login-error',
  // new
  validationRequired: 'validation-error.required',
  isInvalidFormat: 'validation-error.is-invalid-format',
  password: 'validation-error.password',
};

export const LOCIZE_LOGIN_TRANSLATION_KEYS = {
  loginButtonLabel: 'login-button-label',
  smartIdMethodButtonLabel: 'smart-id-method-button-label',
  mobileIdMethodButtonLabel: 'mobile-id-method-button-label',
  idCardMethodButtonLabel: 'id-card-method-button-label',
  passwordMethodButtonLabel: 'password-method-button-label',
  banklinkMethodButtonLabel: 'banklink-method-button-label',
  eParakstsMobileMethodButtonLabel: 'eparaksts-mobile-method-button-label',
  eParakstsSmartCardMethodButtonLabel:
    'eparaksts-smart-card-method-button-label',
  passwordFieldLabel: 'password-field-label',
  userNameFieldLabel: 'username-field-label',
  idCodeFieldLabel: 'id-code-field-label',
  idCodeFieldLabelLV: 'id-code-field-label-lv',
  emailFieldLabel: 'email-field-label',
  loginConfirmationProcessingDisclaimer:
    'login-confirmation-processing-disclaimer',
  customerAwaitingPatienceDisclaimer: 'customer-awaiting-patience-disclaimer',
  customerAwaitingEmailAddressDisclaimer:
    'customer-awaiting-email-address-disclaimer',
  customerAwaitingLoginLinkDisclaimer:
    'customer-awaiting-login-link-disclaimer',
  loginPageHeading: 'login-page-heading',
  phoneFieldLabel: 'phone-field-label',
  idCardLoginDisclaimer: 'id-card-login-disclaimer',
  smartIdFullVersionDisclaimer: 'smart-id-full-version-disclaimer',
  // new
  loginPageTitle: 'login-page-title',
  [AppLoginMethods.smartId]: 'smart-id-method-button-label',
  [AppLoginMethods.mobileId]: 'mobile-id-method-button-label',
  [AppLoginMethods.idCard]: 'id-card-method-button-label',
  [AppLoginMethods.password]: 'password-method-button-label',
  [AppLoginMethods.banklink]: 'banklink-method-button-label',
  [AppLoginMethods.eParakstsMobile]: 'eparaksts-mobile-method-button-label',
  [AppLoginMethods.eParakstsSmartCard]:
    'eparaksts-smart-card-method-button-label',
};

export const LOCIZE_CHECKOUT_TRANSLATION_KEYS = {
  installmentsPeriodSelectTitle: 'installments-period-select-label',
  downPaymentTooltip: 'down-payment-tooltip',
  downPaymentFieldLabel: 'down-payment-field-label',
  purchaseAmount: 'purchase-amount',
  smallLoanMonthlyPaymentDisclaimer: 'sl-monthly-payment-disclaimer',
  smallLoanMarketingHeading: 'sl-marketing-heading',
  smallLoanMarketingText: 'sl-marketing-text',
  creditLineMonthlyPaymentDisclaimer: 'cl-monthly-payment-disclaimer',
  creditLinePageTitle: 'cl-page-title',
  creditLinePageSubtitle: 'cl-page-subtitle',
  creditLineInfoTitle: 'cl-info-title',
  creditLineInfoStep1Title: 'cl-info-step1-title',
  creditLineInfoStep1Subtitle: 'cl-info-step1-subtitle',
  creditLineInfoStep2Title: 'cl-info-step2-title',
  creditLineInfoStep2Subtitle: 'cl-info-step2-subtitle',
  creditLineInfoStep3Title: 'cl-info-step3-title',
  creditLineInfoStep3Subtitle: 'cl-info-step3-subtitle',
  exactMonthlyPaymentDisclaimerTitle: 'exact-monthly-payment-disclaimer-title',
  noInterestOrFeesDisclaimerTitle: 'no-interest-or-fees-disclaimer-title',
  contractFeeTitle: 'contract-fee-title',
  smallLoanMarketingTitle: 'small-loan-marketing-title',
  fastLoanMarketingTitle: 'fast-loan-marketing-title',
  renovationLoanMarketingTitle: 'renovation-loan-marketing-title',
  vehicleLoanMarketingTitle: 'vehicle-loan-marketing-title',
  travelLoanMarketingTitle: 'travel-loan-marketing-title',
  healthLoanMarketingTitle: 'health-loan-marketing-title',
  beautyLoanMarketingTitle: 'beauty-loan-marketing-title',
  travelLoanMarketingSubtitle: 'travel-loan-marketing-subtitle',
  healthLoanMarketingSubtitle: 'health-loan-marketing-subtitle',
  beautyLoanMarketingSubtitle: 'beauty-loan-marketing-subtitle',
  smallLoanMarketingSubtitle: 'small-loan-marketing-subtitle',
  fastLoanMarketingSubtitle: 'fast-loan-marketing-subtitle',
  renovationLoanMarketingSubtitle: 'renovation-loan-marketing-subtitle',
  vehicleLoanMarketingSubtitle: 'vehicle-loan-marketing-subtitle',
  smallLoanGroupCampaignText: 'small-loan-group-campaign-text',
  creditLimitIncreaseTitle: 'clr-title',
  creditLimitIncreaseDisclaimer: 'clr-disclaimer',
  smallLoanGroupNoManagementCampaignText:
    'small-loan-group-no-management-campaign-text',
  specialOfferDescriptionMonth: 'special-offer-description-month',
  specialOfferDescriptionMonths: 'special-offer-description-months',
  paymentScheduleTitle: 'payment-schedule-title',
  smallLoanFeatureTitle1: 'small-loan-feature-title-1',
  smallLoanFeatureDescription1: 'small-loan-feature-description-1',
  smallLoanFeatureTitle2: 'small-loan-feature-title-2',
  smallLoanFeatureDescription2: 'small-loan-feature-description-2',
  smallLoanFeatureTitle3: 'small-loan-feature-title-3',
  smallLoanFeatureDescription3: 'small-loan-feature-description-3',
  smallLoanFeatureTitle4: 'small-loan-feature-title-4',
  smallLoanFeatureDescription4: 'small-loan-feature-description-4',
  smallLoanLimitedOffer: 'small-loan-limited-offer',
  smallLoanLimitedOfferApplyNow: 'small-loan-limited-offer-apply-now',
};

export const LOCIZE_USER_TRANSLATION_KEYS = {
  politicalExposurePrefix: 'political-exposure-',
};

export const LOCIZE_SIGNING_TRANSLATION_KEYS = {
  acceptTermsOfContractWithContractLink:
    'accept-terms-of-contract-with-contract-link',
  acceptTermsOfContractAlternativeWithContractLink:
    'accept-terms-of-contract-alternative-with-contract-link',
  makingDownPaymentDisclaimer: 'making-downpayment-disclaimer',
  anotherLoginMethodDisclaimer: 'another-login-method-disclaimer',
  viewContract: 'view-contract',
  downPaymentFieldLabel: 'down-payment-field-label',
  signingPageHeading: 'signing-page-heading',
  creditLimitSigningHeading: 'credit-line-signing-heading',
  signContract: 'sign-contract',
  signContractWithSmartId: 'sign-contract-with-smart-id',
  signContractWithIdCard: 'sign-contract-with-id-card',
  confirmSignature: 'confirm-signature',
  creditLineSigningWithdrawDisclaimer: 'cl-signing-withdraw-disclaimer',
  maxLimitOfferDisclaimer: 'max-limit-offer-disclaimer',
  moreThanMaxLoanDisclaimer: 'more-than-max-loan-disclaimer',
  signContractWithMobileId: 'sign-contract-with-mobile-id',
  signContractWithBanklink: 'sign-contract-with-banklink',
  signingContractLabel: 'signing-contract-label',
  lvAdditionalDisclaimer: 'lv-additional-disclaimer',
  contractFeeTitle: 'contract-fee-title',
  paymentScheduleTitle: 'payment-schedule-title',
  instantPayoutMethodTypeTitle: 'payout-method-type.instant.title',
  instantPayoutMethodTypeDescription: 'payout-method-type.instant.description',
  regularPayoutMethodTypeTitle: 'payout-method-type.regular.title',
  regularPayoutMethodTypeDescription: 'payout-method-type.regular.description',
  newsletterAgreementText: 'newsletter-agreement.text',
  newsletterAgreementPrivacyPolicy: 'newsletter-agreement.privacy-policy',
  installmentsPeriodSelectLabel: 'installments-period-select-label',
  adjustedOfferActionButton: 'adjusted-offer.action-button',
  adjustedOfferTitle: 'adjusted-offer.title',
  adjustedOfferDescription: 'adjusted-offer.description',
  // new keys in ns signing
  approvedTitle: 'approved-title',
  approvedDescription: 'approved-description',
  decreaseLimitButtonLabel: 'decrease-limit-button-label',
  increaseLimitNotificationText: 'increase-limit-notification-text',
  decreaseLimitModalTitle: 'decrease-limit-modal.title',
  decreaseLimitModalLimitLabel: 'decrease-limit-modal.limit-label',
  decreaseLimitModalDescription: 'decrease-limit-modal.description',
  viewTerms: 'view-terms',
  acceptTermsOfContract: 'accept-terms-of-contract',
  contractTitle: 'contract-title',
  campaignOfferTitle: 'campaign-offer-title',
  changeLoginMethodDisclaimer: 'change-login-method-disclaimer',
  changeLoginMethodButtonLabel: 'change-login-method-button-label',
  chooseBankLabel: 'choose-bank-label',
  ibanTooltipLabel: 'iban-tooltip-label',
  ibanFieldLabel: 'iban-field-label',
};

export const LOCIZE_CONTACT_TRANSLATION_KEYS = {
  termsAndConditionsCheckboxLabel: 'terms-and-conditions-checkbox-label',
  termsAndConditionsAndPensionCheckboxLabel:
    'terms-and-conditions-and-pension-checkbox-label',
  postalCodeFieldLabel: 'postal-code-field-label',
  phoneFieldLabel: 'phone-field-label',
  politicallyExposedPersonPlaceholderLabel: 'pep-placeholder-label',
  politicallyExposedPersonFieldLabel: 'pep-field-label',
  politicallyExposedPersonTooltipLabel: 'pep-tooltip-label',
  politicallyExposedPersonDisclaimerLabel: 'pep-disclaimer-label',
  newsletterCheckboxLabel: 'newsletter-checkbox-label',
  nameFieldLabel: 'name-field-label',
  cityFieldLabel: 'city-field-label',
  emailFieldLabel: 'email-field-label',
  addressFieldLabel: 'address-field-label',
  ibanFieldLabel: 'iban-field-label',
  ibanTooltipLabel: 'iban-tooltip-label',
};

export const LOCIZE_COMMON_TRANSLATION_KEYS = {
  test: 'test',
  back: 'back',
  continue: 'continue',
  loginToContinue: 'login-to-continue',
  submit: 'submit',
  confirm: 'confirm',
  confirmed: 'confirmed',
  footerTopText: 'footer-top-text',
  footerBotText: 'footer-bot-text',
  footerText: 'footer-text', // new
  footerText1: 'footer-text-1', // new
  contactInfoDisclaimer: 'contact-info-disclaimer',
  contactUsDisclaimer: 'contact-us-disclaimer',
  contactUsAffiliateApplicationSigningDisclaimer:
    'contact-us-affiliate-application-signing-disclaimer',
  installmentTableDateColumnName: 'installment-table-date-column-name',
  installmentTableTotalColumnName: 'installment-table-total-column-name',
  chooseLanguage: 'choose-language',
  chooseFileToUpload: 'choose-file-to-upload',
  loanAmountTitle: 'loan-amount-title',
  loanAmountTip: 'loan-amount-tip',
  monthlyPaymentTitle: 'monthly-payment-title',
  monthlyInterestTitle: 'monthly-interest-title',
  signingFeeTitle: 'signing-fee-title',
  contractFeeTitle: 'contract-fee-title',
  paymentScheduleTitle: 'payment-schedule-title',
  managementFeeTitle: 'management-fee-title',
  yourMonthlyPaymentTitle: 'your-monthly-payment-title',
  yourMaximumMonthlyPaymentTitle: 'your-maximum-monthly-payment-title',
  yourMaximumAvailableLimitTitle: 'your-maximum-available-limit-title',
  periodTitle: 'period-title',
  monthTitle: 'month-title',
  monthsTitle: 'months-title',
  estoAccount: 'esto-account',
  estoAccountDisclaimer: 'esto-account-disclaimer',
  estoAccountDisclaimerWithCreditAmount:
    'esto-account-disclaimer-with-credit-amount',
  logoutButtonLabel: 'logout-button-label',
  dontClosePageLabel: 'dont-close-page-label',
  pinConfirmationCodeDisclaimerPin: 'pin-confirmation-code-disclaimer-pin',
  chooseOptionLabel: 'choose-option-label',
  signLabel: 'sign-label',
  successLabel: 'success-label',
  appInProcessLabel: 'app-in-process-label',
  cancelLabel: 'cancel-label',
  defaultDelayedMessageTitle: 'default-delayed-message-title',
  defaultDelayedMessageText: 'default-delayed-message-text',
  specialOfferTitle: 'special-offer-title',
  specialOfferDescriptionMonths: 'special-offer-description-months',
  specialOfferDescriptionMonth: 'special-offer-description-month',
  specialOfferDescriptionDays: 'special-offer-description-days',
  checkIncomeDisclaimer: 'check-income-disclaimer', // old
  checkIncomeDisclaimer2: 'check-income-disclaimer-2', // new
  specialOfferPaymentLeaveDescription:
    'special-offer-payment-leave-description',
  specialOfferPaymentLeaveSmallLoanDescription:
    'special-offer-payment-leave-small-loan-description',
  specialOfferPaymentLeaveTitle: 'special-offer-payment-leave-title',
  firstPaymentDateTitle: 'first-payment-date-title',
  // new
  goToEstoAccountButton: 'go-to-esto-account-button',
  interestFreeCampaignInfo: 'interest-free-campaign-info',
  acceptedFileTypesLabel: 'accepted-file-types',
  addFileButton: 'add-file-button',
  provideFollowingDocuments: 'provide-following-documents',
  dragAndDropFiles: 'drag-and-drop-files',
};

export const LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS = {
  incomeLiabilitiesDisclaimer: 'income-liabilities-disclaimer',
  accessBank: 'access-bank',
  analysingBankStatement: 'analysing-bank-statement',
  finalising: 'finalising',
  followInstructionsDisclaimer: 'follow-instructions-disclaimer',
  followInstructionsInTab: 'follow-instructions-in-tab',
  makingDecision: 'making-decision',
  selectBankDisclaimer: 'select-bank-disclaimer',
  talkingToBank: 'talking-to-bank',
  uploadStatementDisclaimer: 'upload-statement-disclaimer',
  uploadStatementDisclaimer1: 'upload-statement-disclaimer-1',
  verifyingFinancialInfo: 'verifying-financial-info',
  // New keys (ns account-scoring-v2)
  verificationMethodTitle: 'verification-method-title',
  accountScoringMethodTitle: 'account-scoring-method-title',
  accountScoringMethodRecommended: 'account-scoring-method-recommended',
  accountScoringMethodDescription: 'account-scoring-method-description',
  otherMethodsButton: 'other-methods-button',
  otherMethodsTitle: 'other-methods-title',
  otherMethodsDescription: 'other-methods-description',
  emtaTitle: 'emta-title',
  emtaDescription: 'emta-description',
  startButton: 'start-button',
  uploadBankStatementTitle: 'upload-bank-statement-title',
  uploadBankStatementDescription: 'upload-bank-statement-description',
  acceptedFileTypesLabel: 'accepted-file-types-label',
  uploadButton: 'upload-button',
  problemsVerifyingWithAccountScoringDisclaimer:
    'problems-verifying-with-account-scoring-disclaimer',
  // verifyingFinancialInfo: 'verifying-financial-info' (is needed account-scoring-v2)
  keepPageOpenDisclaimer: 'keep-page-open-disclaimer',
  manualUploadPendingDisclaimer: 'manual-upload-pending-disclaimer',
  applicationInReview: 'application-in-review',
  otherVerificationMethodsDisclaimer: 'other-verification-methods-disclaimer',
  confirmTitle: 'confirm.title',
  confirmDescription: 'confirm.description',
};

export const LOCIZE_REJECT_TRANSLATION_KEYS = {
  faq: 'faq',
  reasonsDisclaimer: 'reasons-disclaimer',
  defaultRejectionDisclaimer: 'default-rejection-disclaimer',
  returnToMerchant: 'return-to-merchant',
  smallLoanRejectionHeading: 'small-loan-rejection-heading',
  smallLoanRejectionCredyHeading: 'small-loan-rejection-credy-heading',
  creditLineRejectionCredyHeading: 'credit-line-rejection-credy-heading',
  creditLineRejectionHeading: 'credit-line-rejection-heading',
  smallLoanDefaultRejectionDisclaimer:
    'small-loan-default-rejection-disclaimer',
  smallLoanCredyRejectionDisclaimer: 'small-loan-credy-rejection-disclaimer',
  creditLineCredyRejectionDisclaimer: 'credit-line-credy-rejection-disclaimer',
  creditLineDefaultRejectionDisclaimer: 'credit-line-rejection-disclaimer',
  credyDisclaimer: 'credy-disclaimer',
  credyHeading: 'credy-heading',
  rejectReasonsDisclaimer: 'reject-reasons-disclaimer',
  //new
  creditLineRejectionReasons: 'credit-line-rejection-reasons-btn',
  creditLineDefaultRejectionDisclaimer2: 'credit-line.rejection-disclaimer2',
  creditLineRejectionWithCredyHeading:
    'credit-line.rejection-with-credy-heading',
  creditLineRejectionWithCredyDisclaimer:
    'credit-line.rejection-with-credy-disclaimer',
  creditLineRejectionWithCredyButton: 'credit-line.rejection-with-credy-button',
};

export const LOCIZE_SUCCESS_TRANSLATION_KEYS = {
  afterMakingDownpaymentDisclaimer: 'after-making-down-payment-disclaimer',
  backToMerchant: 'back-to-merchant',
  clickButtonDisclaimer: 'click-button-disclaimer',
  proceedToWithdrawal: 'proceed-to-withdrawal',
  makeDownpayment: 'make-down-payment',
  signStandingPayment: 'sign-standing-payment',
  successfulSigningDisclaimer: 'successful-signing-disclaimer',
  successfulSmallLoanSigningHeading: 'successful-sl-signing-heading',
  successfulCreditLineSigningHeading: 'successful-cl-signing-heading',
  successfulSmallLoanSigningDisclaimer: 'successful-sl-signing-disclaimer',
  successfulSmallLoanSigningWithSPDisclaimer:
    'successful-sl-signing-disclaimer-with-standing-payment',
  successfulSmallLoanSigningWithoutSPDisclaimer:
    'successful-sl-signing-disclaimer-without-standing-payment',
  successfulAffiliateSmallLoanSigningDisclaimer:
    'successful-affiliate-sl-signing-disclaimer',
  successfulCreditLineSigningDisclaimer: 'successful-cl-signing-disclaimer',
  successfulInstantPayoutDisclaimer:
    'successful-sl-signing-disclaimer-instant-payout',
  // new
  successLabel: 'success-label',
  creditLineDisclaimer: 'credit-line-disclaimer',
  withdrawButtonLabel: 'withdraw-button-label',
  estoAccountButtonLabel: 'esto-account-button-label',
  newsletterAgreementTitle: 'newsletter-agreement.title',
  newsletterAgreementTitle2: 'newsletter-agreement.title2',
  newsletterAgreementDisclaimer: 'newsletter-agreement.disclaimer',
  newsletterAgreementButtonLabel: 'newsletter-agreement.button-label',
};

export const LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS = {
  financialDataDisclaimer: 'financial-data-disclaimer',
  perMonthPlaceholder: 'per-month-placeholder',
  netIncomeLabel: 'net-income-label',
  netIncomeTooltipLabel: 'net-income-tooltip-label',
  netIncomeLabelLt: 'net-income-label-lt',
  netIncomeTooltipLabelLt: 'net-income-tooltip-label-lt',
  totalExpensesLabel: 'total-expenses-label',
  expenditureLabel: 'expenditure-label',
  expenditureTooltipLabel: 'expenditure-tooltip-label',
  dependentsLabel: 'dependents-label',
  livingExpensesPerMonthLabel: 'living-expenses-per-month-label',
  livingExpensesPerMonthTooltipLabel: 'living-expenses-per-month-tooltip-label',
  employmentDateLabel: 'employment-date-label',
  employmentDateFirstOptionLabel: 'employment-date-first-option-label',
  employmentDateSecondOptionLabel: 'employment-date-second-option-label',
  employmentDateThirdOptionLabel: 'employment-date-third-option-label',
  planningNewDebtsLabel: 'planning-new-debts-label',
  planningNewDebtsCheckboxLabel: 'planning-new-debts-checkbox-label',
  futureReducedEarningsLabel: 'future-reduced-earnings-label',
  futureReducedEarningsCheckboxLabel: 'future-reduced-earnings-checkbox-label',
  ultimateBeneficialOwnerLabel: 'ultimate-beneficial-owner-label',
  ultimateBeneficialOwnerTooltip: 'ultimate-beneficial-owner-tooltip',
  instructionsSendingMethodLabel: 'instructions-sending-method-label',
  instructionsSendingMethodPlaceholder:
    'instructions-sending-method-placeholder',
  phoneOptionLabel: 'phone-option-label',
  emailOptionLabel: 'email-option-label',
  spouseEmailFieldLabel: 'spouse-email-field-label',
  spousePhoneFieldLabel: 'spouse-phone-field-label',
  personalSectionTitle: 'personal-section-title',
  spouseSectionTitle: 'spouse-section-title',
  spouseSectionIntro: 'spouse-section-intro',
  spouseSectionSubmitButton: 'spouse-section-submit-button',
  instructionsSentDisclaimer: 'instructions-sent-disclaimer',
  changeInstructionsButton: 'change-instructions-button',
  occupationCategoryFieldLabel: 'occupation-category-field-label',
  occupationCategoryFieldPlaceholder: 'occupation-category-field-placeholder',
  legalPersonFieldPlaceholder: 'legal-person-field-placeholder',
  addLegalPersonFieldLabel: 'add-legal-person-field-label',
  occupationCategoryPrefix: 'occupation-category',
  overdueDebtLabel: 'overdue-debt-label',
  overdueDebtCheckboxLabel: 'overdue-debt-checkbox-label',
  overdueDebtTooltipLabel: 'overdue-debt-tooltip-label',
};

export const LOCIZE_PENDING_TRANSLATION_KEYS = {
  pendingHeading: 'pending-heading',
  pendingDisclaimer: 'pending-disclaimer',
  spouseConsentPendingDisclaimer: 'spouse-consent-pending-disclaimer',
  outsideWorkingHoursPendingDisclaimer:
    'outside-working-hours-pending-disclaimer',
  checkIncomeButtonLabel: 'check-income-button-label',
  manualScoringNeededDisclaimer: 'manual-scoring-needed-disclaimer',
  //new
  creditLineDefaultPendingDisclaimer: 'credit-line.default-pending-disclaimer',
  creditLinePendingWorkingHoursDisclaimer:
    'credit-line.pending-working-hours-disclaimer',
  creditLineCheckIncomeDisclaimer: 'credit-line.check-income-disclaimer',
  creditLineCheckIncomeHeading: 'credit-line.check-income-heading',
  creditLineDebtDisclaimer: 'credit-line.debt-disclaimer',
  creditLineDebtHeading: 'credit-line.debt-heading',
  creditLineDebtButton: 'credit-line.debt-button',
  debtDisclaimer: 'debt-disclaimer',
  debtHeading: 'debt-heading',
  payDebtButton: 'pay-debt-button',
  creditLineWithdrawalOnboardingDisclaimer:
    'credit-line-withdrawal-onboarding-disclaimer',
  creditLineWithdrawalOnboardingCTAButtonLabel:
    'credit-line-withdrawal-onboarding-cta-button-label',
  creditLineOnboardingDisclaimer: 'credit-line-onboarding-disclaimer',
  creditLineOnboardingCTAButtonLabel: 'credit-line-onboarding-cta-button-label',
};

export const LOCIZE_LOAN_TYPE_TRANSLATION_KEYS = {
  pageTitle: 'page-title',
  personalLoan: 'personal-loan',
  personalLoanDescription: 'personal-loan-description',
  familyLoan: 'family-loan',
  familyLoanDescription: 'family-loan-description',
  pendingDisclaimer: 'family-loan-description',
  // new
  purposePersonalOptionLabel: 'purpose.personal-option-label',
  purposeFamilyOptionLabel: 'purpose.family-option-label',
  spouseApplicationMethodFieldLabel: 'spouse.method-field-label',
  spouseEmailFieldLabel: 'spouse.email-field-label',
  spousePhoneFieldLabel: 'spouse.phone-field-label',
  spouseEmailOptionLabel: 'spouse.email-option-label',
  spousePhoneOptionLabel: 'spouse.phone-option-label',
  spouseMotivationalDisclaimer: 'spouse.motivational-disclaimer',
};

//new
export const LOCIZE_CREDIT_LINE_TRANSLATION_KEYS = {
  title: 'credit-line',
  checkoutSliderTitle: 'checkout.slider-title',
  learnMoreButton: 'learn-more-button',
  bannerDescription1: 'banner-description-1',
  bannerDescription2: 'banner-description-2',
  bannerDescription3: 'banner-description-3',
  bannerDescription3LT: 'banner-description-3-lt',
  bannerDescription4: 'banner-description-4',
  fagQuote: 'fag.quote',
  fagQuoteDescription: 'fag.quote-description',
  faqItem1Question: 'faq.item-1-question',
  faqItem1Answer: 'faq.item-1-answer',
  faqItem2Question: 'faq.item-2-question',
  faqItem2Answer: 'faq.item-2-answer',
  faqItem3Question: 'faq.item-3-question',
  faqItem3Answer: 'faq.item-3-answer',
  faqItem4Question: 'faq.item-4-question',
  faqItem4Answer: 'faq.item-4-answer',
  faqItem5Question: 'faq.item-5-question',
  faqItem5Answer: 'faq.item-5-answer',
  faqSupportQuestion: 'faq.support-question',
  faqSupportButton: 'faq.support-button',
  campaignBannerTitle: 'campaign-banner-title',
  campaignBannerDescription: 'campaign-banner-description',
  applicationInReview: 'application-in-review',
  pendingEstoAccountButton: 'pending.esto-account-button',
};

export const LOCIZE_SPOUSE_CONSENT_TRANSLATION_KEYS = {
  spouseConsentTitle: 'spouse-consent-title',
  spouseConsentSubtitle: 'spouse-consent-subtitle',
  chooseSigningMethodsLabel: 'choose-signing-methods-label',
  spousePendingHeading: 'spouse-pending-heading',
  phoneFieldLabel: 'phone-field-label',
  employmentDateLabel: 'employment-date-label',
  overdueDebtTooltipLabel: 'overdue-debt-tooltip-label',
  overdueDebtCheckboxLabel: 'overdue-debt-checkbox-label',
  overdueDebtLabel: 'overdue-debt-label',
  livingExpensesPerMonthTooltipLabel: 'living-expenses-per-month-tooltip-label',
  livingExpensesPerMonthLabel: 'living-expenses-per-month-label',
  expenditureTooltipLabel: 'expenditure-tooltip-label',
  expenditureLabel: 'expenditure-label',
  netIncomeTooltipLabel: 'net-income-tooltip-label',
  netIncomeLabel: 'net-income-label',
  netIncomeLabelLt: 'net-income-label-lt',
  netIncomeTooltipLabelLt: 'net-income-tooltip-label-lt',
  spouseConsentHeading: 'spouse-consent-heading',
  spouseConsentDescription: 'spouse-consent-description',
  signingDisclaimer: 'signing-disclaimer',
};

export const LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS = {
  emtaConsentTitle: 'emta-consent-title',
  emtaConsentDescription: 'emta-consent-description',
  emtaConsentButtonTitle: 'emta-consent-button-title',
  emtaConsentButtonDescription: 'emta-consent-button-description',
  accountScoringButtonTitle: 'account-scoring-button-title',
  accountScoringButtonDescription: 'account-scoring-button-description',
  retryEmtaDisclaimer: 'retry-emta-disclaimer',
  retryEmtaButton: 'retry-emta-button',
  retryAccountScoringButton: 'retry-account-scoring-button',
  // New keys (ns emta-consent-v2)
  verificationMethodTitle: 'verification-method-title',
  emtaMethodTitle: 'emta-method-title',
  emtaMethodRecommended: 'emta-method-recommended',
  emtaMethodDescription: 'emta-method-description',
  otherMethodsButton: 'other-methods-button',
  otherMethodsTitle: 'other-methods-title',
  otherMethodsDescription: 'other-methods-description',
  internetBankTitle: 'internet-bank-title',
  internetBankDescription: 'internet-bank-description',
  startButton: 'start-button',
  problemsVerifyingWithEmtaDisclaimer:
    'problems-verifying-with-emta-disclaimer',
  verifyingFinancialInfo: 'verifying-financial-info',
  keepPageOpenDisclaimer: 'keep-page-open-disclaimer',
  confirmTitle: 'confirm.title',
  confirmDescription: 'confirm.description',
};

export const LOCIZE_SCHEDULE_TRANSLATION_KEYS = {
  regularScheduleTitle: 'regular-schedule-title',
  regularScheduleDescription: 'regular-schedule-description',
  xScheduleDescription: 'x-schedule-description',
  payLaterScheduleDescription: 'pay-later-schedule-description',
  payScheduleDescription: 'pay-schedule-description',
  choosePaymentMethod: 'choose-payment-method',
};

export const LOCIZE_AFTER_DIRECT_PAYMENT_TRANSLATION_KEYS = {
  newsletterModalActionBtn: 'newsletter-modal-action-btn',
  newsletterModalDisclaimer: 'newsletter-modal-disclaimer',
  newsletterModalTitle: 'newsletter-modal-title',
  paymentFailureBackToMerchantBtn: 'payment-failure-back-to-merchant-btn',
  paymentFailureDisclaimer: 'payment-failure-disclaimer',
  paymentFailureEstoAccountBtn: 'payment-failure-esto-account-btn',
  paymentFailureRetryBtn: 'payment-failure-retry-btn',
  paymentFailureTitle: 'payment-failure-title',
  paymentSuccessDisclaimer: 'payment-success-disclaimer',
  paymentSuccessEstoAccountBtn: 'payment-success-esto-account-btn',
  paymentSuccessBackToMerchantBtn: 'payment-success-back-to-merchant-btn',
  paymentSuccessNewsletterBtn: 'payment-success-newsletter-btn',
  paymentSuccessSwitchBtn: 'payment-success-switch-btn',
  paymentSuccessTitle: 'payment-success-title',
  emailFieldPlaceholder: 'email-field-placeholder',
  newsletterSubscriptionSuccess: 'newsletter-subscription-success',
};

export const LOCIZE_OFFER_TRANSLATION_KEYS = {
  disclaimerTitle: 'disclaimer-title',
  disclaimerDescription: 'disclaimer-description',
  disclaimerHirePurchaseDescription: 'disclaimer-hire-purchase-description',
  purchaseAmount: 'purchase-amount',
  paymentPeriod: 'payment-period',
  monthlyPaymentTitle: 'monthly-payment-title',
};

export const LOCIZE_ERROR_TRANSLATION_KEYS = {
  errorHeading: 'error-heading',
  errorDescription: 'error-description',
};

// new

export const LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS = {
  occupationTitle: 'occupation.title',
  occupationLabel: 'occupation.label',
  occupationCategoryPrefix: 'occupation-category',
  farmersDocument1: 'farmers-document-1',
  farmersDocument2: 'farmers-document-2',
  employeeDocument1: 'employee-document-1',
  noAdditionalDocumentsNeeded: 'no-additional-documents',
  soleBusinessOwnerDocument1: 'sole-business-owner-document-1',
  selfEmployedDocument1: 'self-employed-document-1',
  selfEmployedDocument2: 'self-employed-document-2',
  selfEmployedDocument3: 'self-employed-document-3',
  seafarersDocument1: 'seafarers-document-1',
  seafarersDocument2: 'seafarers-document-2',
  workingAbroadDocument1: 'working-abroad-document-1',
  authorSportsPersonDocument1: 'author-sports-person-document-1',
  authorSportsPersonDocument2: 'author-sports-person-document-2',
  clericDocument1: 'cleric-document-1',
  dailyAllowanceDocument1: 'daily-allowance-document-1',
  disabilityAllowanceDocument1: 'disability-allowance-document-1',
  disabilityAllowanceDocument2: 'disability-allowance-document-2',
  foreignPensionerDocument1: 'foreign-pensioner-document-1',
  lawyerDocument1: 'lawyer-document-1',
  matemrityAllowanceDocument1: 'matemrity-allowance-document-1',
  rentDocument1: 'rent-document-1',
  occupationAddFileButton: 'occupation.add-file',
};

export const LOCIZE_INCOME_INSURANCE_TRANSLATION_KEYS = {
  bannerTitle: 'banner-title',
  bannerDescription: 'banner-description',
  ctaButtonLabel: 'cta-button-label',
  changeLoginMethodDisclaimer: 'change-login-method-disclaimer',
  changeLoginMethodButtonLabel: 'change-login-method-button-label',
  signing: {
    rangeLabel: 'signing.range-label',
    perMonthLabel: 'signing.per-month-label',
    monthlyCostLabel: 'signing.monthly-cost-label',
    termsOfServiceCheckboxLabel: 'signing.terms-of-service-checkbox-label',
    dataPolicyCheckboxLabel: 'signing.data-policy-checkbox-label',
    healthAndEmploymentCheckboxLabel:
      'signing.health-and-employment-checkbox-label',
    signButtonLabel: 'signing.sign-button-label',
    phoneLabel: 'signing.phone-field-label',
    emailLabel: 'signing.email-field-label',
  },
  success: {
    title: 'success.title',
    disclaimer: 'success.disclaimer',
    myAccount: 'success.my-account',
  },
  reject: {
    title: 'reject.title',
    disclaimer: 'reject.disclaimer',
    tryAgain: 'reject.try-again',
    myAccount: 'reject.my-account',
    support: 'reject.support',
  },
  rejectReason: {
    ageIsOutOfRange: 'reject-reason.age-is-out-of-range',
    unpaidInvoice: 'reject-reason.unpaid-invoice',
  },
};
