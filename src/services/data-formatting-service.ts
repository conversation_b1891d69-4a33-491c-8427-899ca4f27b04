import type {
  Application,
  ApplicationUserInfo,
  CreditAccount,
  CreditAccountLimitRecalculation,
  CreditAccountWithdrawal,
} from 'api/core/generated';
import type { AnyObject } from 'models';

export const formatUserToContactPageDataFormat = (user: AnyObject | null) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,
    fullName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    politicalExposure: user?.political_exposure,
    conditionsAgreement: user?.conditions_agreement,
    allowPensionQuery: user?.allow_pension_query,
    newsletterAgreement: user?.newsletter_agreement,

    applicationUserInfoId: creditAccount?.application_user_info_id,
  };
};

export const formatApplicationToContactPageDataFormat = (
  application?: Application,
) => {
  const {
    email,
    phone,
    city,
    address,
    post_code,
    iban,
    purpose_of_loan,
    phone_area_code,
    first_name,
    last_name,
  } = application?.user_info ?? {};

  return {
    email,
    phone,
    city,
    address,
    postCode: post_code,
    iban,
    purposeOfLoan: purpose_of_loan,
    phoneAreaCode: phone_area_code,
    productId: application?.id,
    userId: application?.user_id,
    fullName: `${first_name ?? ''} ${last_name ?? ''}`,
    politicalExposure: application?.user?.political_exposure,
    allowPensionQuery: application?.user?.allow_pension_query,
    conditionsAgreement: application?.user?.conditions_agreement,
    newsletterAgreement: application?.user?.newsletter_agreement,

    applicationUserInfoId: application?.application_user_info_id,
  };
};
export const formatCreditLineWithdrawalToContactPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = creditAccountWithdrawal?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,
    fullName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    politicalExposure: user?.political_exposure,
    allowPensionQuery: user?.allow_pension_query,
    conditionsAgreement: user?.conditions_agreement,
    newsletterAgreement: user?.newsletter_agreement,

    applicationUserInfoId: creditAccountWithdrawal?.application_user_info_id,
  };
};
export const formatCreditLimitRecalculationToContactPageDataFormat = (
  creditLimitRecalculation: CreditAccountLimitRecalculation,
) => {
  const creditAccount = creditLimitRecalculation?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,
    fullName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    politicalExposure: user?.political_exposure,
    conditionsAgreement: user?.conditions_agreement,
    allowPensionQuery: user?.allow_pension_query,
    newsletterAgreement: user?.newsletter_agreement,

    applicationUserInfoId: creditLimitRecalculation?.application_user_info_id,
  };
};

// CONTACT EXTRA

export const formatApplicationToContactExtraPageDataFormat = (
  application: Application,
) => {
  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    phone_area_code,
    occupation_category,
  } = (application.user_info ?? {}) as ApplicationUserInfo;

  return {
    eligibilityState: application?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: phone_area_code,
    productId: application.id,
    userId: application.user_id,

    applicationUserInfoId: application?.application_user_info_id,
    scheduleType: application.schedule_type,
    isPrivatePerson: application.for_private_person,
    legalPerson:
      application.legal_person_info?.legal_person_score?.legal_person_id,

    legalPersonInfo: application.legal_person_info!,
    addLegalPersonToInvoice: application.add_legal_person_to_invoice,
    overdueDebt: application.user?.profile?.overdue_debt,
  };
};

export const formatUserToContactExtraPageDataFormat = (
  user: AnyObject | null,
) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;
  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  } = user?.profile ?? {};

  return {
    eligibilityState: creditAccount?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccount?.application_user_info_id,
    overdueDebt: overdue_debt,
  };
};

export const formatCreditLineWithdrawalToContactExtraPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = (creditAccountWithdrawal?.credit_account ??
    {}) as CreditAccount;
  const user = creditAccount?.user;

  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  } = user?.profile ?? {};

  return {
    eligibilityState: creditAccountWithdrawal?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccountWithdrawal?.application_user_info_id,
    scheduleType: null,
    isPrivatePerson: true,
    legalPerson: null,
    legalPersonInfo: null,
    addLegalPersonToInvoice: false,
    activeLegalPeopleOptions: [],
    overdueDebt: overdue_debt,
  };
};

export const formatCreditLimitRecalculationToContactExtraPageDataFormat = (
  creditLimitRecalculation: CreditAccountLimitRecalculation,
) => {
  const creditAccount = (creditLimitRecalculation?.credit_account ??
    {}) as CreditAccount;

  const user = creditAccount?.user;

  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  } = user?.profile ?? {};

  return {
    eligibilityState: creditLimitRecalculation?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditLimitRecalculation?.application_user_info_id,
    scheduleType: null,
    isPrivatePerson: true,
    legalPerson: null,
    legalPersonInfo: null,
    addLegalPersonToInvoice: false,
    overdueDebt: overdue_debt,
  };
};

// ACCOUNT SCORING

export const formatApplicationToAccountScoringPageDataFormat = (
  application: Application,
) => {
  const { first_name, last_name, email } = application.user_info ?? {};

  return {
    email: email,
    userName: `${first_name ?? ''} ${last_name ?? ''}`,
    language: application.user?.language_abbr,
    productId: application.id,
    userId: application.user_id,
    productIdVariables: {
      credit_account_id: null,
      application_id: application.id,
    },
  };
};

export const formatUserToAccountScoringPageDataFormat = (
  user: AnyObject | null,
) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;

  return {
    email: user?.email,
    userName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    language: user?.language_abbr,
    productId: creditAccount?.id,
    userId: user?.id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

export const formatCreditLineWithdrawalToAccountScoringPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = creditAccountWithdrawal?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    userName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    language: user?.language_abbr,
    productId: creditAccount?.id,
    userId: user?.id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

export const formatCreditLimitRecalculationToAccountScoringPageDataFormat = (
  creditLimitRecalculation: CreditAccountLimitRecalculation,
) => {
  const creditAccount = creditLimitRecalculation?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    userName: `${user?.profile?.first_name ?? ''} ${
      user?.profile?.last_name ?? ''
    }`,
    language: user?.language_abbr,
    productId: creditAccount?.id,
    userId: user?.id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

// SPOUSE CONSENT

export const formatApplicationToSpouseConsentPageDataFormat = (
  application: Application,
) => {
  const {
    spouse_first_name,
    spouse_last_name,
    spouse_employment_date,
    spouse_net_income_monthly,
    spouse_expenditure_monthly,
    spouse_monthly_living_expenses,
  } = application.user_info ?? {};

  return {
    spouseName: `${spouse_first_name ?? ''} ${spouse_last_name ?? ''}`,
    spouseEmploymentDate: spouse_employment_date,
    spouseNetIncomeMonthly: spouse_net_income_monthly,
    spouseExpenditureMonthly: spouse_expenditure_monthly,
    spouseMonthlyLivingExpenses: spouse_monthly_living_expenses,
    spouseOverdueDebt: application.user?.profile?.spouse_overdue_debt,
    productId: application.id,

    applicationUserInfoId: application?.application_user_info_id,
    productIdVariables: {
      credit_account_id: null,
      application_id: application.id,
    },
  };
};

export const formatUserToSpouseConsentPageDataFormat = (
  creditAccount: CreditAccount | null,
) => {
  const {
    spouse_first_name,
    spouse_last_name,
    spouse_net_income_monthly,
    spouse_expenditure_monthly,
    spouse_monthly_living_expenses,
    spouse_employment_date,
  } = creditAccount?.user_info ?? {};

  return {
    spouseName: `${spouse_first_name ?? ''} ${spouse_last_name ?? ''}`,
    spouseEmploymentDate: spouse_employment_date,
    spouseNetIncomeMonthly: spouse_net_income_monthly,
    spouseExpenditureMonthly: spouse_expenditure_monthly,
    spouseMonthlyLivingExpenses: spouse_monthly_living_expenses,
    spouseOverdueDebt: creditAccount?.user?.profile?.spouse_overdue_debt,
    productId: creditAccount?.id,

    applicationUserInfoId: creditAccount?.application_user_info_id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

export const formatCreditLineWithdrawalToSpouseConsentPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = creditAccountWithdrawal?.credit_account;
  const {
    spouse_first_name,
    spouse_last_name,
    spouse_net_income_monthly,
    spouse_expenditure_monthly,
    spouse_monthly_living_expenses,
    spouse_employment_date,
  } = creditAccount?.user_info ?? {};

  return {
    spouseName: `${spouse_first_name ?? ''} ${spouse_last_name ?? ''}`,
    spouseEmploymentDate: spouse_employment_date,
    spouseNetIncomeMonthly: spouse_net_income_monthly,
    spouseExpenditureMonthly: spouse_expenditure_monthly,
    spouseMonthlyLivingExpenses: spouse_monthly_living_expenses,
    spouseOverdueDebt: creditAccount?.user?.profile?.spouse_overdue_debt,
    productId: creditAccount?.id,

    applicationUserInfoId: creditAccountWithdrawal?.application_user_info_id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

export const formatCreditLimitRecalculationToSpouseConsentPageDataFormat = (
  creditLimitRecalculation: CreditAccountLimitRecalculation,
) => {
  const creditAccount = creditLimitRecalculation?.credit_account;
  const {
    spouse_first_name,
    spouse_last_name,
    spouse_net_income_monthly,
    spouse_expenditure_monthly,
    spouse_monthly_living_expenses,
    spouse_employment_date,
  } = creditAccount?.user_info ?? {};

  return {
    spouseName: `${spouse_first_name ?? ''} ${spouse_last_name ?? ''}`,
    spouseEmploymentDate: spouse_employment_date,
    spouseNetIncomeMonthly: spouse_net_income_monthly,
    spouseExpenditureMonthly: spouse_expenditure_monthly,
    spouseMonthlyLivingExpenses: spouse_monthly_living_expenses,
    spouseOverdueDebt: creditAccount?.user?.profile?.spouse_overdue_debt,
    productId: creditAccount?.id,

    applicationUserInfoId: creditLimitRecalculation?.application_user_info_id,
    productIdVariables: {
      credit_account_id: creditAccount?.id,
      application_id: null,
    },
  };
};

// PURPOSE OF LOAN

export const formatUserToPurposeOfLoanPageDataFormat = (
  user: AnyObject | null,
) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan ?? null,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccount?.application_user_info_id,
  };
};

export const formatApplicationToPurposeOfLoanPageDataFormat = (
  application: Application,
) => {
  const { email, phone, city, address, post_code, purpose_of_loan, iban } =
    application.user_info ?? {};

  return {
    email,
    phone,
    city,
    address,
    postCode: post_code,
    iban,
    purposeOfLoan: purpose_of_loan ?? null,
    productId: application.id,
    userId: application.user_id,

    applicationUserInfoId: application?.application_user_info_id,
  };
};

export const formatCreditLineWithdrawalToPurposeOfLoanPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = creditAccountWithdrawal?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan ?? null,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccountWithdrawal?.application_user_info_id,
  };
};

export const formatCreditLimitRecalculationToPurposeOfLoanPageDataFormat = (
  creditLimitRecalculation: CreditAccountLimitRecalculation,
) => {
  const creditAccount = creditLimitRecalculation?.credit_account;
  const user = creditAccount?.user;

  return {
    email: user?.email,
    phone: user?.phone,
    city: user?.profile?.city,
    address: user?.profile?.address,
    postCode: user?.profile?.post_code,
    iban: user?.profile?.iban,
    purposeOfLoan: creditAccount?.user_info?.purpose_of_loan ?? null,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditLimitRecalculation?.application_user_info_id,
  };
};

// SIGNING

export const formatUserToSigningPageDataFormat = (user: AnyObject | null) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;

  return {
    iban: user?.profile?.iban,
    applicationUserInfoId: creditAccount?.application_user_info_id ?? null,
  };
};

export const formatApplicationToSigningPageDataFormat = (
  application: Application,
) => {
  const { iban } = application.user_info ?? {};

  return {
    iban,
    applicationUserInfoId: application?.application_user_info_id ?? null,
  };
};
// INCOME VERIFICATION

export const formatUserToIncomeVerificationPageDataFormat = (
  user: AnyObject | null,
) => {
  const creditAccount = (user?.credit_accounts ?? [])[0] as
    | CreditAccount
    | undefined;
  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  } = user?.profile ?? {};

  return {
    eligibilityState: creditAccount?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccount?.application_user_info_id,
    overdueDebt: overdue_debt,
  };
};

export const formatApplicationToIncomeVerificationPageDataFormat = (
  application: Application,
) => {
  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    phone_area_code,
    occupation_category,
  } = (application.user_info ?? {}) as ApplicationUserInfo;

  return {
    eligibilityState: application?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: phone_area_code,
    productId: application.id,
    userId: application.user_id,

    applicationUserInfoId: application?.application_user_info_id,
    scheduleType: application.schedule_type,
    isPrivatePerson: application.for_private_person,
    legalPerson:
      application.legal_person_info?.legal_person_score?.legal_person_id,

    legalPersonInfo: application.legal_person_info!,
    addLegalPersonToInvoice: application.add_legal_person_to_invoice,
    overdueDebt: application.user?.profile?.overdue_debt,
  };
};

export const formatCreditLimitRecalculationToIncomeVerificationPageDataFormat =
  (creditLimitRecalculation: CreditAccountLimitRecalculation) => {
    const creditAccount = (creditLimitRecalculation?.credit_account ??
      {}) as CreditAccount;

    const user = creditAccount?.user;

    const {
      net_income_monthly,
      expenditure_monthly,
      number_of_dependents,
      monthly_living_expenses,
      employment_date,
      planning_new_debts,
      future_reduced_earnings,
      ultimate_beneficial_owner,
      occupation_category,
      overdue_debt,
    } = user?.profile ?? {};

    return {
      eligibilityState: creditLimitRecalculation?.eligibility_state,
      netIncomeMonthly: net_income_monthly,
      expenditureMonthly: expenditure_monthly,
      monthlyLivingExpenses: monthly_living_expenses,
      numberOfDependents: number_of_dependents,
      employmentDate: employment_date,
      planningNewDebts: planning_new_debts,
      futureReducedEarnings: future_reduced_earnings,
      ultimateBeneficialOwner: ultimate_beneficial_owner,
      occupationCategory: occupation_category,
      phoneAreaCode: user?.phone_area_code,
      productId: creditAccount?.id,
      userId: user?.id,
      applicationUserInfoId: creditLimitRecalculation?.application_user_info_id,
      scheduleType: null,
      isPrivatePerson: true,
      legalPerson: null,
      legalPersonInfo: null,
      addLegalPersonToInvoice: false,
      overdueDebt: overdue_debt,
    };
  };

export const formatCreditLineWithdrawalToIncomeVerificationPageDataFormat = (
  creditAccountWithdrawal: CreditAccountWithdrawal,
) => {
  const creditAccount = (creditAccountWithdrawal?.credit_account ??
    {}) as CreditAccount;
  const user = creditAccount?.user;

  const {
    net_income_monthly,
    expenditure_monthly,
    number_of_dependents,
    monthly_living_expenses,
    employment_date,
    planning_new_debts,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  } = user?.profile ?? {};

  return {
    eligibilityState: creditAccountWithdrawal?.eligibility_state,
    netIncomeMonthly: net_income_monthly,
    expenditureMonthly: expenditure_monthly,
    monthlyLivingExpenses: monthly_living_expenses,
    numberOfDependents: number_of_dependents,
    employmentDate: employment_date,
    planningNewDebts: planning_new_debts,
    futureReducedEarnings: future_reduced_earnings,
    ultimateBeneficialOwner: ultimate_beneficial_owner,
    occupationCategory: occupation_category,
    phoneAreaCode: user?.phone_area_code,
    productId: creditAccount?.id,
    userId: user?.id,

    applicationUserInfoId: creditAccountWithdrawal?.application_user_info_id,
    scheduleType: null,
    isPrivatePerson: true,
    legalPerson: null,
    legalPersonInfo: null,
    addLegalPersonToInvoice: false,
    activeLegalPeopleOptions: [],
    overdueDebt: overdue_debt,
  };
};
