import type { FC, PropsWithChildren, ReactNode } from 'react';

type DualPanelLayoutProps = PropsWithChildren<{
  left: ReactNode;
  right: ReactNode;
  rightPanelId: string;
}>;

export const DualPanelLayout: FC<DualPanelLayoutProps> = ({
  left,
  right,
  rightPanelId,
}) => (
  <div className="h-full relative grid grid-cols-1 md:m-auto md:size-full md:grid-cols-2 md:grid-rows-1 md:grid-areas-[left_right]">
    {left ? (
      <div className="w-full flex items-center justify-end md:bg-neutral-50 md:grid-in-[left]">
        <div className=" flex flex-col justify-normal h-full w-full md:justify-center">
          {left}
        </div>
      </div>
    ) : null}
    {right ? (
      <div
        id={rightPanelId}
        className="w-full flex items-center justify-start md:grid-in-[right]"
      >
        <div className=" flex flex-col justify-normal h-full w-full md:justify-center">
          {right}
        </div>
      </div>
    ) : null}
  </div>
);
