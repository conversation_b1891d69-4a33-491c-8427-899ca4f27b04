import { useIsMobileView } from 'hooks/system';
import type { ReactNode } from 'react';
import { cn } from 'utils/tailwind';
import { MainHeader } from 'widgets/headers/main-header';

import { ContainerLayout } from '../container-layout/ContainerLayout';
import { DualPanelLayout } from '../dual-panel-layout/DualPanelLayout';

type PageLayoutProps = {
  left: ReactNode;
  right: ReactNode;
  leftHeader?: ReactNode;
  leftFooter?: ReactNode;
  rightHeader?: ReactNode;
  className?: string;
  withLeftContainer?: boolean;
  rightFooter?: ReactNode;
};

export const PageLayout = ({
  left,
  right,
  leftHeader,
  rightHeader,
  leftFooter,
  className,
  withLeftContainer = true,
  rightFooter,
}: PageLayoutProps) => {
  const isMobileView = useIsMobileView();
  return (
    <div
      className={cn(
        'relative flex flex-col min-h-screen md:h-full md:block',
        className,
      )}
    >
      {isMobileView ? (
        <ContainerLayout
          className={cn(
            'sticky top-0 z-10 bg-primary-white md:relative md:bg-transparent',
            isMobileView && 'border-b border-solid border-neutral-200',
          )}
        >
          <MainHeader />
        </ContainerLayout>
      ) : null}

      <DualPanelLayout
        rightPanelId="rightDualPanel"
        left={
          <>
            {leftHeader && leftHeader}
            {withLeftContainer ? (
              <ContainerLayout
                id="leftBody"
                className="grid grid-cols-1 mt-auto overflow-y-auto place-items-center no-scrollbar"
                noYPadding
              >
                <div className="flex flex-col w-full items-center justify-center max-w-[25rem]">
                  {left && left}
                </div>
              </ContainerLayout>
            ) : (
              left && left
            )}
            {leftFooter && leftFooter}
          </>
        }
        right={
          <>
            {rightHeader && rightHeader}
            <ContainerLayout
              id="rightBody"
              className="pt-8 grid grid-cols-1 justify-center items-center pb-20 overflow-y-auto no-scrollbar md:py-0 md:h-full bg-white"
            >
              <div className="h-full flex flex-col w-full max-w-[25rem] mx-auto place-items-center justify-center content-center md:[&>*:first-child:not(#default-loader)]:pb-[5.125rem] md:[&>*:first-child:not(#default-loader)]:pt-[5.5rem]">
                {right && right}
              </div>
            </ContainerLayout>
            {rightFooter && rightFooter}
          </>
        }
      />
    </div>
  );
};
