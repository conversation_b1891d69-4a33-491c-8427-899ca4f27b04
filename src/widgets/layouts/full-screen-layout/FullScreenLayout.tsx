import type { PropsWithChildren } from 'react';
import { cn } from 'utils/tailwind';
import { MainHeader } from 'widgets/headers/main-header';

import { ContainerLayout } from '../container-layout';

type FullScreenLayoutProps = {
  className?: string;
  contentToTop?: boolean;
};

export const FullScreenLayout = ({
  children,
  className,
  contentToTop = false,
}: PropsWithChildren<FullScreenLayoutProps>) => (
  <div>
    <div
      className={cn(
        'fixed top-0 left-0 bg-primary-white w-full h-full z-20',
        className,
      )}
    >
      <div className="max-w-360 mx-auto h-full">
        <ContainerLayout className="z-20 static top-0 bg-primary-white md:relative md:bg-transparent h-16 md:h-20">
          <MainHeader />
        </ContainerLayout>
        <div
          className={cn(
            'h-[calc(100%-5rem)] overflow-y-auto pb-20 px-6 flex flex-col justify-center items-center no-scrollbar',
            contentToTop && 'justify-start',
          )}
        >
          <div
            className={cn(
              'flex flex-col items-center w-full max-w-100 mx-auto my-auto',
              contentToTop && 'my-0',
            )}
          >
            {children}
          </div>
        </div>
      </div>
    </div>
  </div>
);
