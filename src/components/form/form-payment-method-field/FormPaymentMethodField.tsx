import type { PayseraPaymentMethod } from 'api/core/generated';
import { FormField, FormItem } from 'components/ui/form';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { cn } from 'utils/tailwind';

type FormPaymentMethodFieldProps<
  TFieldValues extends FieldValues = FieldValues,
> = {
  name: Path<TFieldValues>;
  control: Control<TFieldValues>;
  options: (PayseraPaymentMethod | Record<string, string> | null)[];
  invalid?: boolean;
  disabled?: boolean;
  className?: string;
  containerClassName?: string;
  selectedValue?: string;
  onSelect?: (value: string | number | null) => void;
};

export const FormPaymentMethodField = <
  TFieldValues extends FieldValues = FieldValues,
>({
  name,
  control,
  options,
  invalid,
  disabled,
  className,
  containerClassName,
  selectedValue,
  onSelect,
}: FormPaymentMethodFieldProps<TFieldValues>) => (
  <FormField
    control={control}
    name={name}
    render={({ field, formState }) => (
      <FormItem className={cn('w-full', containerClassName)}>
        <div className="inline-flex flex-wrap justify-center gap-4 rounded-lg mt-5">
          {options?.map((method) => {
            if (!method) {
              return null;
            }

            const { key, title, logo_url } = method;

            return (
              <button
                key={key}
                className={cn(
                  'flex h-12 w-[7.6125rem] items-center justify-center gap-2 rounded-[0.875rem] border border-neutral-200 bg-white-primary px-4 py-1 transition-colors hover:border-neutral-200 hover:shadow-none hover:bg-gray-50',
                  selectedValue === key &&
                    '!border-primary-black shadow-none cursor-not-allowed hover:bg-transparent',
                  formState.errors[name] && 'border-red-500',
                  invalid && 'border-red-500',
                  className,
                )}
                onClick={() => {
                  field.onChange(key);
                  onSelect?.(key);
                }}
                disabled={disabled}
                type="button"
              >
                <img className="w-21.75" alt={title} src={logo_url} />
              </button>
            );
          })}
        </div>
      </FormItem>
    )}
  />
);
