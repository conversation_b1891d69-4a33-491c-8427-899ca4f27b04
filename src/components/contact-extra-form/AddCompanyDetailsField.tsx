import {
  FormFieldNames,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormSelectField } from 'components/form/form-select-field';
import { Skeleton } from 'components/ui/skeleton';
import type { Option } from 'models';
import { useEffect } from 'react';
import type { FieldValues, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

type AddCompanyDetailsFieldProps<TFormData extends FieldValues> = {
  form: UseFormReturn<TFormData>;
  visible?: boolean;
  selectOptions: Option[];
  isDisabled?: boolean;
  legalPeopleLoading?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
};

export const AddCompanyDetailsField = <TFormData extends FieldValues>({
  form,
  visible,
  selectOptions,
  onChange,
  isDisabled = false,
  legalPeopleLoading = false,
  className,
}: AddCompanyDetailsFieldProps<TFormData>) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);

  const addLegalPersonToInvoiceValue = form.watch(
    FormFieldNames.addLegalPersonToInvoice as any,
  );

  useEffect(() => {
    if (!addLegalPersonToInvoiceValue) {
      form.clearErrors(FormFieldNames.legalPerson as any);
    }
  }, [addLegalPersonToInvoiceValue, form]);

  useEffect(() => {
    if (isDisabled && addLegalPersonToInvoiceValue) {
      form.setValue(
        FormFieldNames.addLegalPersonToInvoice as any,
        false as any,
      );
    }
  }, [isDisabled, addLegalPersonToInvoiceValue, form]);

  useEffect(() => {
    if (onChange) {
      onChange(addLegalPersonToInvoiceValue);
    }
  }, [addLegalPersonToInvoiceValue, onChange]);

  if (!visible) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className || ''}`}>
      <FormCheckboxField<TFormData>
        control={form.control}
        name={FormFieldNames.addLegalPersonToInvoice as any}
        label={t(
          LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.addLegalPersonFieldLabel,
        )}
        disabled={isDisabled}
      />

      {addLegalPersonToInvoiceValue && (
        <div className="ml-6 space-y-2">
          {legalPeopleLoading || !selectOptions.length ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          ) : (
            <FormSelectField<TFormData>
              control={form.control}
              name={FormFieldNames.legalPerson as any}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.legalPersonFieldPlaceholder,
              )}
              options={selectOptions}
              disabled={isDisabled}
            />
          )}
        </div>
      )}
    </div>
  );
};
