import {
  ABBREVIATIONS_BY_LANGUAGES_MAP,
  FLAG_ICONS_BY_LANGUAGE_MAP,
  LANGUAGES_BY_ABBREVIATIONS_MAP,
} from 'app-constants';
import { Typography } from 'components/typography';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from 'components/ui/dropdown-menu';
import { useRootContext } from 'context/root';
import { useGetLanguages, useUpdateUserLanguage } from 'hooks';
import { ChevronDownIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useToggle } from 'react-use';
import { changeZendeskLocale } from 'services';
import { cn } from 'utils/tailwind';

import { AppIcon } from '../app-icon';

type LanguageSelectorProps = {
  className?: string;
};

export const LanguageSelector = ({ className }: LanguageSelectorProps) => {
  const { i18n } = useTranslation();

  const { languages } = useGetLanguages();
  const [isDropdownMenuOpen, handleDropdownMenuToggle] = useToggle(false);
  const { updateUserLanguage } = useUpdateUserLanguage();
  const { user } = useRootContext();

  const handleLanguageChange = (lang: string) => async () => {
    try {
      await i18n.changeLanguage(lang);

      if (user) {
        await updateUserLanguage({
          user_id: user.id,
          language_abbr: ABBREVIATIONS_BY_LANGUAGES_MAP[lang],
        });
      }

      changeZendeskLocale(lang);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <DropdownMenu onOpenChange={handleDropdownMenuToggle}>
      <DropdownMenuTrigger className="focus-visible:outline-none cursor-pointer">
        <div className={cn('flex items-center', className)}>
          <AppIcon
            size={{ width: 16, height: 16 }}
            name={FLAG_ICONS_BY_LANGUAGE_MAP[i18n.language]}
            className="[clip-path:circle(50%)]"
          />
          <Typography variant="text-s" className="text-inherit ml-2.5">
            {i18n.language.toUpperCase()}
          </Typography>
          <ChevronDownIcon
            className={cn(
              'ml-0.5 w-3.5 h-3.5',
              isDropdownMenuOpen && 'rotate-180 transform',
            )}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-30">
        {languages.map(({ abbreviation }) => {
          const language = LANGUAGES_BY_ABBREVIATIONS_MAP[abbreviation];
          return (
            <DropdownMenuCheckboxItem
              key={abbreviation}
              arrowPosition="right"
              checked={i18n.language === language}
              onCheckedChange={handleLanguageChange(language)}
              className="cursor-pointer"
            >
              <AppIcon
                size={{ width: 16, height: 16 }}
                name={FLAG_ICONS_BY_LANGUAGE_MAP[language]}
              />
              <Typography variant="text-s" className="text-inherit ml-2.5">
                {language.toUpperCase()}
              </Typography>
            </DropdownMenuCheckboxItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
