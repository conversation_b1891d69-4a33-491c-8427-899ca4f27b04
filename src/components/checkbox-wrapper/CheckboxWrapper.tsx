import { InfoTooltip } from 'components/info-tooltip/InfoTooltip';
import { Checkbox } from 'components/ui/checkbox';
import { Label } from 'components/ui/label';
import { type PropsWithChildren, type ReactNode, useId, useState } from 'react';
import { cn } from 'utils/tailwind';

type CheckboxWrapperProps = PropsWithChildren & {
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  label: ReactNode;
  className?: string;
  checked?: boolean;
  invalid?: boolean;
  info?: string;
};

export const CheckboxWrapper = ({
  onCheckedChange,
  disabled,
  label,
  className,
  checked = false,
  children,
  invalid,
  info,
}: CheckboxWrapperProps) => {
  const id = useId();
  const [isChecked, setIsChecked] = useState(checked);

  return (
    <div className={cn(className)}>
      <div className={'flex flex-row px-2.5 space-y-0 cursor-pointer'}>
        <Checkbox
          className="mt-0.5"
          id={id}
          onCheckedChange={(checked) => {
            setIsChecked(!!checked);
            onCheckedChange?.(!!checked);
          }}
          checked={isChecked}
          invalid={invalid}
          disabled={disabled}
        />
        <div className="space-y-1 leading-none flex gap-2">
          <Label
            htmlFor={id}
            className={cn(
              'cursor-pointer pl-3 flex',
              disabled && 'cursor-not-allowed',
            )}
          >
            {label}
          </Label>
          {info && (
            <InfoTooltip
              className={cn(
                'w-fit mt-1.5! mr-[0.0875rem] inline!',
                disabled && 'cursor-not-allowed',
              )}
              text={info}
              iconClassName="h-4 w-4 text-neutral-400"
            />
          )}
        </div>
      </div>
      <div
        className={`space-y-2 pl-10 transition-all duration-500 ease ${
          isChecked
            ? 'max-h-[500px] opacity-100 mt-2'
            : 'max-h-0 opacity-0 overflow-hidden mt-0'
        }`}
      >
        {children}
      </div>
    </div>
  );
};
