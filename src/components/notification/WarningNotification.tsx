import { Typography } from 'components/typography';
import WarningIcon from 'icons/warning.svg?react';
import type { FC, PropsWithChildren } from 'react';
import { cn } from 'utils/tailwind';

import { Notification } from './Notification';

interface WarningNotificationProps extends PropsWithChildren {
  className?: string;
}

export const WarningNotification: FC<WarningNotificationProps> = ({
  children,
  className,
}) => (
  <Notification
    className={cn(
      className,
      'grid grid-cols-[auto_1fr] gap-2 rounded-lg border-solid border-[0.0625rem] border-red-500 bg-neutral-100 bg-primary-white p-2.5 shadow-[0px_6px_10px_0px_rgba(42,40,135,0.05)]',
    )}
    icon={
      <WarningIcon className="mt-1 size-4.5 text-primary-brand-02 md:mt-[0.025rem]" />
    }
  >
    <Typography tag="div" variant="text-s">
      {children}
    </Typography>
  </Notification>
);
