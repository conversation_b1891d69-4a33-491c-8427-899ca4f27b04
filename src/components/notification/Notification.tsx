import c from 'clsx';
import { Typography } from 'components/typography';
import InfoIcon from 'icons/info.svg?react';
import type { FC, PropsWithChildren } from 'react';

interface NotificationProps extends PropsWithChildren {
  className?: string;
  icon?: React.ReactNode;
}

export const Notification: FC<NotificationProps> = ({
  children,
  className,
  icon = <InfoIcon className="mt-[0.05rem] size-4.5 text-primary-brand-02" />,
}) => (
  <div
    className={c(
      'grid grid-cols-[auto_1fr] gap-2 rounded-lg border border-transparent bg-neutral-100 bg-primary-white p-2.5 shadow-[0px_6px_10px_0px_rgba(42,40,135,0.05)]',
      className,
    )}
  >
    {icon}
    <Typography tag="div" variant="text-s">
      {children}
    </Typography>
  </div>
);
