import { Popover, PopoverContent, PopoverTrigger } from 'components/ui/popover';
import {
  Tooltip as TooltipPrimitive,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/ui/tooltip';
import { useIsMobileDevice } from 'hooks';
import type { FC, PropsWithChildren } from 'react';

type TooltipProps = PropsWithChildren<{
  text: string;
  asChild?: boolean;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}>;

export const Tooltip: FC<TooltipProps> = ({
  children,
  text,
  asChild,
  className = 'max-w-100',
  open,
  onOpenChange,
}) => {
  const isMobileDevice = useIsMobileDevice();

  if (isMobileDevice) {
    return (
      <Popover modal open={open} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild={asChild} type="button">
          {children}
        </PopoverTrigger>
        <PopoverContent className={className}>{text}</PopoverContent>
      </Popover>
    );
  }

  return (
    <TooltipProvider delayDuration={0}>
      <TooltipPrimitive>
        <TooltipTrigger asChild={asChild} type="button">
          {children}
        </TooltipTrigger>
        <TooltipContent className={className}>
          <p>{text}</p>
        </TooltipContent>
      </TooltipPrimitive>
    </TooltipProvider>
  );
};
