import EstoLogoIcon from 'icons/logo.svg?react';
import BankIcon from 'icons/scoring-loader/bank.svg?react';
import EuroIcon from 'icons/scoring-loader/euro.svg?react';
import UserIcon from 'icons/scoring-loader/user.svg?react';
import { useEffect, useState } from 'react';
import { cn } from 'utils/tailwind';

// animation timings here are synced with the animations setup in the tailwind.config.ts

const ANIMATION_INTERVAL = 3900;

export const CircleLoader = ({ className }: { className?: string }) => {
  const [animatePulse1, setAnimatePulse1] = useState(false);
  const [animatePulse2, setAnimatePulse2] = useState(false);
  const [animatePulse3, setAnimatePulse3] = useState(false);
  const [animatePulse4, setAnimatePulse4] = useState(false);

  useEffect(() => {
    setAnimatePulse1(true);
    setTimeout(() => {
      setAnimatePulse1(false);
    }, 500);

    const id = setInterval(() => {
      setAnimatePulse1(true);
      setTimeout(() => {
        setAnimatePulse1(false);
      }, 500);
    }, ANIMATION_INTERVAL);
    return () => {
      clearInterval(id);
    };
  }, []);

  useEffect(() => {
    setAnimatePulse2(true);
    setTimeout(() => {
      setAnimatePulse2(false);
    }, 700);

    const id = setInterval(() => {
      setAnimatePulse2(true);
      setTimeout(() => {
        setAnimatePulse2(false);
      }, 700);
    }, ANIMATION_INTERVAL);
    return () => {
      clearInterval(id);
    };
  }, []);

  useEffect(() => {
    setAnimatePulse3(true);
    setTimeout(() => {
      setAnimatePulse3(false);
    }, 1000);

    const id = setInterval(() => {
      setAnimatePulse3(true);
      setTimeout(() => {
        setAnimatePulse3(false);
      }, 1000);
    }, ANIMATION_INTERVAL);
    return () => {
      clearInterval(id);
    };
  }, []);

  useEffect(() => {
    setAnimatePulse4(true);
    setTimeout(() => {
      setAnimatePulse4(false);
    }, 2900);

    const id = setInterval(() => {
      setAnimatePulse4(true);
      setTimeout(() => {
        setAnimatePulse4(false);
      }, 2900);
    }, ANIMATION_INTERVAL);
    return () => {
      clearInterval(id);
    };
  }, []);

  return (
    <div className={cn('relative w-92.5 h-92.5', className)}>
      <div
        className={cn(
          'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[235px] h-[235px] rounded-full z-10 animate-rotate-bank',
        )}
      >
        <BankIcon className="animate-rotate-reverse-bank" />
      </div>
      <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] -rotate-90 w-[176px] h-[176px] rounded-full z-10 animate-rotate-user">
        <UserIcon className="animate-rotate-reverse-user rotate-90" />
      </div>
      <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] rotate-90 w-[176px] h-[176px] rounded-full animate-rotate-euro z-10">
        <EuroIcon className="animate-rotate-reverse-euro -rotate-90" />
      </div>

      <div
        className={cn(
          'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[370px] h-[370px] rounded-full',
          'bg-[radial-gradient(circle_at_center,#E2E2E2,#FFFFFF_70%)]',
          animatePulse4 && 'animate-pulse-3',
        )}
      >
        <div
          className={cn(
            'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[290px] h-[290px] rounded-full',
            'bg-[radial-gradient(circle_at_center,#E2E2E2,#FCFCFC_66%)]',
            'border border-solid border-white',
            animatePulse3 && 'animate-pulse-2',
          )}
        />
        <div
          className={cn(
            'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[210px] h-[210px] rounded-full',
            'bg-[radial-gradient(circle_at_center,#E2E2E2,#FCFCFC_66%)]',
            animatePulse2 && 'animate-pulse',
          )}
        />
        <div
          className={cn(
            'absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[130px] h-[130px] rounded-full',
            'transform-origin-center',
            'border border-solid border-white',
            'bg-[radial-gradient(circle_at_center,#E2E2E2,#FCFCFC_66%)]',
            animatePulse1 && 'animate-pulse-and-pump',
          )}
        >
          <EstoLogoIcon className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]" />
        </div>
      </div>
    </div>
  );
};
