import { Toaster as Sonner } from 'sonner';

type ToasterProps = React.ComponentProps<typeof Sonner>;

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg gap-1!',
          title:
            'group-[.toast]:text-[.875rem] group-[.toast]:leading-4.5! group-[.toast]:tracking-[-.00875rem] group-[.toast]:font-semibold',
          description:
            'group-[.toast]:text-[.875rem] leading-4.5! group-[.toast]:tracking-[-.00875rem]! group-[.toast]:font-normal',
        },
      }}
      {...props}
    />
  );
};

export { Toaster };
