import {
  type SmartIdPrepareSignatureMutationVariables,
  type SmartIdSignContractMutationVariables,
  useSmartIdPrepareSignatureMutation,
  useSmartIdSignContractMutation,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';

export const useSignContractWithSmartId = () => {
  const smartIdSignController = new AbortController();

  const [prepareForSmartIdSigning, { data, loading, error }] =
    useSmartIdPrepareSignatureMutation({
      context: { apiVersion: AppApiVersions.core },
    });

  const [
    signSmartIdContract,
    { data: smartIdSignatureData, error: smartIdSignatureError },
  ] = useSmartIdSignContractMutation();

  const prepareSmartIdContractSignature = (
    variables: SmartIdPrepareSignatureMutationVariables,
  ) => prepareForSmartIdSigning({ variables });

  const signContractWithSmartId = (
    variables: SmartIdSignContractMutationVariables = {},
  ) =>
    signSmartIdContract({
      variables,
      context: {
        apiVersion: AppApiVersions.core,
        fetchOptions: {
          signal: smartIdSignController.signal,
        },
      },
    });

  return {
    smartIdContractSignaturePreparationLoading: loading,
    smartIdContractSignaturePreparationChallenge: data?.challenge,
    smartIdContractSignaturePreparationError: error,
    signContractWithSmartIdSuccess: smartIdSignatureData?.success,
    signContractWithSmartIdError: smartIdSignatureError,
    prepareSmartIdContractSignature,
    signContractWithSmartId,
  };
};
