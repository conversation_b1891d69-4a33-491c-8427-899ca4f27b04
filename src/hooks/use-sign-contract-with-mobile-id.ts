import {
  type MobileIdPrepareSignatureMutationVariables,
  type MobileIdSignContractMutationVariables,
  useMobileIdPrepareSignatureMutation,
  useMobileIdSignContractMutation,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';

export const useSignContractWithMobileId = () => {
  const mobileIdSignController = new AbortController();

  const [prepareForMobileIdSigning, { data, loading, error }] =
    useMobileIdPrepareSignatureMutation({
      context: { apiVersion: AppApiVersions.core },
    });

  const [
    signMobileIdContract,
    { data: mobileIdSignatureData, error: mobileIdSignatureError },
  ] = useMobileIdSignContractMutation();

  const prepareMobileIdContractSignature = (
    variables: MobileIdPrepareSignatureMutationVariables,
  ) => prepareForMobileIdSigning({ variables });

  const signContractWithMobileId = (
    variables: MobileIdSignContractMutationVariables = {},
  ) =>
    signMobileIdContract({
      variables,
      context: {
        apiVersion: AppApiVersions.core,
        signal: mobileIdSignController.signal,
      },
    });

  return {
    mobileIdContractSignaturePreparationLoading: loading,
    mobileIdContractSignaturePreparationChallenge: data?.challenge,
    mobileIdContractSignaturePreparationError: error,
    signContractWithMobileIdSuccess: mobileIdSignatureData?.success,
    signContractWithMobileIdError: mobileIdSignatureError,
    prepareMobileIdContractSignature,
    signContractWithMobileId,
  };
};
