import {
  NetworkStatus,
  type SuspenseQueryHookOptions,
  useApolloClient,
} from '@apollo/client';
import {
  type Application,
  ApplicationByReferenceDocument,
  type ApplicationByReferenceQuery,
  type ApplicationByReferenceQueryVariables,
  useApplicationByReferenceSuspenseQuery,
} from 'api/core/generated';
import { AppApiVersions, AppSearchParams } from 'app-constants';
import { useSearchParams } from 'react-router-dom';

export const useGetApplicationByReferenceSuspense = (
  options?: SuspenseQueryHookOptions<
    ApplicationByReferenceQuery,
    ApplicationByReferenceQueryVariables
  >,
) => {
  const [searchParams] = useSearchParams();
  const reference_key = searchParams.get(AppSearchParams.referenceKey) ?? '';
  const client = useApolloClient();

  const { data, error, networkStatus, refetch } =
    useApplicationByReferenceSuspenseQuery({
      variables: { reference_key },
      context: { apiVersion: AppApiVersions.core },
      fetchPolicy: 'network-only',
      ...options,
    });

  const quietApplicationRefetch = async () => {
    const result = await client.query<ApplicationByReferenceQuery>({
      query: ApplicationByReferenceDocument,
      variables: { reference_key },
      context: { apiVersion: AppApiVersions.core },
      fetchPolicy: 'network-only',
    });

    return result.data.application;
  };

  const getApplication = () => refetch();

  return {
    application: (data?.application ?? {}) as Application,
    applicationLoading: networkStatus === NetworkStatus.loading,
    applicationError: error,
    applicationReferenceKey: reference_key,
    applicationWasFetched: networkStatus === NetworkStatus.ready,
    quietApplicationRefetch,
    getApplication,
  };
};
