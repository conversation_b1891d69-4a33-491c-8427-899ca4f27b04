import { zodResolver } from '@hookform/resolvers/zod';
import type { Legal<PERSON>erson } from 'api/core/generated';
import {
  ELIGIBILITY_STATES,
  FormFieldNames,
  GoogleAnalyticsEvents,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { isEtRegion, regionPhonePrefix } from 'environment';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetOccupationCategories } from 'hooks/use-get-occupation-categories';
import { useGetPageAttributes } from 'hooks/use-get-page-attributes';
import { useGetUserWithLegal } from 'hooks/use-get-user-with-legal';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useSendConsentLinkToSpouse } from 'hooks/use-send-consent-link-to-spouse';
import { useUpdateApplication } from 'hooks/use-update-application';
import { useUpdateUserInfoExtra } from 'hooks/use-update-user-info-extra';
import { useEffectOnce } from 'hooks/utils';
import type { Option } from 'models';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatApplicationToContactExtraPageDataFormat,
  formatEmploymentDateValueToOptionValue,
  getApplicationStatusForMarketing,
  getEmploymentDateOptions,
  getLegalPeopleOptions,
  getOccupationCategoriesOptions,
  roundNumberUpByTwoDecimals,
} from 'services';
import { processNumberOfDependents } from 'utils/number-of-dependents';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import * as z from 'zod';

const SmallLoanContactExtraFormSchema = z.object({
  [FormFieldNames.netIncomeMonthly]: z.number().nullable(),
  [FormFieldNames.expenditureMonthly]: z.number().nullable(),
  [FormFieldNames.monthlyLivingExpenses]: z.number().nullable(),
  [FormFieldNames.numberOfDependents]: isEtRegion
    ? z.number({ required_error: 'This field is required' })
    : z.number().nullable(),
  [FormFieldNames.totalExpenses]: z.number().nullable(),
  [FormFieldNames.employmentDate]: z.string().nullable(),
  [FormFieldNames.planningNewDebts]: z.number().nullable(),
  [FormFieldNames.futureReducedEarnings]: z.number().nullable(),
  [FormFieldNames.ultimateBeneficialOwner]: z.boolean(),
  [FormFieldNames.occupationCategory]: z.string().nullable(),
  [FormFieldNames.overdueDebt]: z.number().nullable(),
  [FormFieldNames.addLegalPersonToInvoice]: z.boolean().optional(),
  [FormFieldNames.legalPerson]: z.string().nullable().optional(),
});

export type SmallLoanContactExtraFormType = z.infer<
  typeof SmallLoanContactExtraFormSchema
>;

export const useContactExtraPageLogicModern = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);

  const { getPageUrlAndNavigate, trackGoogleAnalyticsEvent } = useRootContext();

  const [instructionsSent, setInstructionsSent] = useState(false);
  const { pageAttributes, pageAttributesLoading, getPageAttributes } =
    useGetPageAttributes();
  const { logAction } = useLogApplicationAction();

  const { getUserWithLegal, userWithLegal, userWithLegalLoading } =
    useGetUserWithLegal();
  const [legalPeopleOptions, setLegalPeopleOptions] = useState<Array<Option>>(
    [],
  );

  const { updateUserInfoExtra, userInfoExtraUpdateError } =
    useUpdateUserInfoExtra();
  const {
    sendConsentLinkToSpouse,
    sendingConsentLink,
    sendingConsentLinkError,
  } = useSendConsentLinkToSpouse();
  const { getOccupationCategories, occupationCategories } =
    useGetOccupationCategories();

  const { updateApplication } = useUpdateApplication();

  const { getApplication, application, applicationLoading } =
    useGetCurrentApplication();

  const {
    ultimateBeneficialOwner,
    futureReducedEarnings,
    planningNewDebts,
    employmentDate,
    numberOfDependents,
    monthlyLivingExpenses,
    expenditureMonthly,
    netIncomeMonthly,
    occupationCategory,
    addLegalPersonToInvoice,
    legalPerson,
    productId,
    phoneAreaCode,
    eligibilityState,
    applicationUserInfoId,
    isPrivatePerson,
    scheduleType,
    overdueDebt,
  } = formatApplicationToContactExtraPageDataFormat({
    ...application,
  });

  const employmentDateOptions = getEmploymentDateOptions(t);

  const form = useForm<SmallLoanContactExtraFormType>({
    resolver: zodResolver(SmallLoanContactExtraFormSchema),
    defaultValues: {
      [FormFieldNames.netIncomeMonthly]: netIncomeMonthly ?? null,
      [FormFieldNames.expenditureMonthly]: expenditureMonthly ?? null,
      [FormFieldNames.monthlyLivingExpenses]: monthlyLivingExpenses ?? null,
      [FormFieldNames.numberOfDependents]: isEtRegion
        ? (numberOfDependents ?? undefined)
        : (numberOfDependents ?? null),
      [FormFieldNames.totalExpenses]: roundNumberUpByTwoDecimals(
        Number(monthlyLivingExpenses ?? 0) + Number(expenditureMonthly ?? 0),
      ),
      [FormFieldNames.employmentDate]: formatEmploymentDateValueToOptionValue(
        employmentDate ?? '',
        employmentDateOptions,
      ) as string | null,
      [FormFieldNames.planningNewDebts]: planningNewDebts ?? null,
      [FormFieldNames.futureReducedEarnings]: futureReducedEarnings ?? null,
      [FormFieldNames.ultimateBeneficialOwner]:
        ultimateBeneficialOwner ?? false,
      [FormFieldNames.occupationCategory]: occupationCategory ?? null,
      [FormFieldNames.overdueDebt]: overdueDebt ?? null,
      [FormFieldNames.addLegalPersonToInvoice]:
        addLegalPersonToInvoice ?? false,
      [FormFieldNames.legalPerson]: legalPerson ? String(legalPerson) : null,
    },
  });

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const contactExtraPageLoaded = !pageAttributesLoading && !applicationLoading;

  const userInfoExtraValidationErrors = useMemo(
    () => extractValidationErrors(userInfoExtraUpdateError),
    [userInfoExtraUpdateError],
  );

  const sendingConsentLinkValidationErrors = useMemo(
    () => extractValidationErrors(sendingConsentLinkError),
    [sendingConsentLinkError],
  );

  const occupationCategoryOptions = useMemo(
    () => getOccupationCategoriesOptions(t, occupationCategories || []),
    [occupationCategories, t],
  );

  // Watch for changes in expenses to update total
  const expenditureValue = form.watch(FormFieldNames.expenditureMonthly);
  const livingExpensesValue = form.watch(FormFieldNames.monthlyLivingExpenses);
  const previousTotalRef = useRef<number | null>(null);

  useEffect(() => {
    const total = roundNumberUpByTwoDecimals(
      Number(expenditureValue ?? 0) + Number(livingExpensesValue ?? 0),
    );

    // Only update if the total has actually changed
    if (previousTotalRef.current !== total) {
      previousTotalRef.current = total;
      form.setValue(FormFieldNames.totalExpenses, total);
    }
  }, [expenditureValue, livingExpensesValue]);

  useEffectOnce(() => {
    getPageAttributes();
    getApplication();
    getUserWithLegal();
    getOccupationCategories();
  });

  useEffect(() => {
    if (userWithLegal?.legal_people_active) {
      setLegalPeopleOptions(
        getLegalPeopleOptions(
          userWithLegal.legal_people_active.filter(
            Boolean,
          ) as Array<LegalPerson>,
        ),
      );
    }
  }, [userWithLegal]);

  const onContactExtraFormSubmit = async (
    data: SmallLoanContactExtraFormType,
  ) => {
    const variablesFilteredByVisiblePageAttributes =
      filterObjectByExistingKeysInObject(
        {
          number_of_dependents: processNumberOfDependents(
            data.number_of_dependents,
          ),
          expenditure_monthly: data.expenditure_monthly,
          monthly_living_expenses: data.monthly_living_expenses,
          net_income_monthly: data.net_income_monthly,
          planning_new_debts: data.planning_new_debts ?? 0,
          employment_date: data.employment_date,
          future_reduced_earnings: data.future_reduced_earnings ?? 0,
          ultimate_beneficial_owner: data.ultimate_beneficial_owner,
          occupation_category: data.occupation_category,
          overdue_debt: data.overdue_debt ?? 0,
        },
        form.control._fields,
      );

    try {
      const appEligibilityState =
        eligibilityState ?? ELIGIBILITY_STATES.pending;
      await updateUserInfoExtra({
        application_user_info_id: applicationUserInfoId ?? 0,
        reject_when_necessary: true,
        extra_income: 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      if (
        visiblePageAttributes[FormFieldNames.addLegalPersonToInvoice] &&
        scheduleType
      ) {
        await updateApplication({
          application_id: productId,
          for_private_person: isPrivatePerson,
          schedule_type: scheduleType,
          legal_person_id: data.legal_person ? Number(data.legal_person) : null,
          add_legal_person_to_invoice:
            data.add_legal_person_to_invoice ?? false,
        });
      }

      await getPageUrlAndNavigate(true);
      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactExtraCompleted, {
        status: getApplicationStatusForMarketing(appEligibilityState),
      });
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }

      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  const handleSendConsentLink = async (data: {
    spouse_email?: string;
    spouse_phone?: string;
  }) => {
    const response = await sendConsentLinkToSpouse({
      application_id: productId,
      ...data,
    });

    if (!response.data?.success) {
      throw new Error('Failed to send consent link to spouse');
    }
  };

  const onAddLegalPersonToInvoiceChange = (checked: boolean) => {
    form.setValue(FormFieldNames.addLegalPersonToInvoice, checked);
    if (!checked) {
      form.setValue(FormFieldNames.legalPerson, null);
    }
  };

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactExtraInfo,
      });
    }
  }, [productId]);

  return {
    form,
    onContactExtraFormSubmit,
    contactExtraPageLoaded,
    visiblePageAttributes,
    userInfoExtraValidationErrors,
    sendingConsentLinkValidationErrors,
    sendingConsentLink,
    occupationCategoryOptions,
    employmentDateOptions,
    legalPeopleOptions,
    legalPeopleLoading: userWithLegalLoading,
    addLegalPersonToInvoiceDisabled: Boolean(
      userWithLegal && !userWithLegal?.legal_people_active?.length,
    ),
    phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
    instructionsSent,
    setInstructionsSent,
    handleSendConsentLink,
    onAddLegalPersonToInvoiceChange,
  };
};
